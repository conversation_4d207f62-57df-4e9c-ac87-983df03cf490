# Copyright (c) OpenMMLab. All rights reserved.
from copy import deepcopy
import os.path as osp
from typing import Dict, List, Optional, Sequence, Union

import cv2
import math
import numpy as np
import torch

import torch.nn.functional as F

from numpy import dtype

import mmengine
from mmengine import dump

from .structures import (
    Det3DDataSample, InstanceData, SampleList,
)


def convert_to_datasample(
    data_samples: SampleList,
    data_instances_3d: Optional[List[InstanceData]] = None,
    data_instances_2d: Optional[List[InstanceData]] = None,
) -> SampleList:
    """Convert results list to `Det3DDataSample`.

    Subclasses could override it to be compatible for some multi-modality
    3D detectors.

    Args:
        data_samples (list[:obj:`Det3DDataSample`]): The input data.
        data_instances_3d (list[:obj:`InstanceData`], optional): 3D
            Detection results of each sample.
        data_instances_2d (list[:obj:`InstanceData`], optional): 2D
            Detection results of each sample.

    Returns:
        list[:obj:`Det3DDataSample`]: Detection results of the
        input. Each Det3DDataSample usually contains
        'pred_instances_3d'. And the ``pred_instances_3d`` normally
        contains following keys.

        - scores_3d (Tensor): Classification scores, has a shape
          (num_instance, )
        - labels_3d (Tensor): Labels of 3D bboxes, has a shape
          (num_instances, ).
        - bboxes_3d (Tensor): Contains a tensor with shape
          (num_instances, C) where C >=7.

        When there are image prediction in some models, it should
        contains  `pred_instances`, And the ``pred_instances`` normally
        contains following keys.

        - scores (Tensor): Classification scores of image, has a shape
          (num_instance, )
        - labels (Tensor): Predict Labels of 2D bboxes, has a shape
          (num_instances, ).
        - bboxes (Tensor): Contains a tensor with shape
          (num_instances, 4).
    """

    assert (data_instances_2d is not None) or \
            (data_instances_3d is not None),\
            'please pass at least one type of data_samples'

    if data_instances_2d is None:
        data_instances_2d = [
            InstanceData() for _ in range(len(data_instances_3d))
        ]
    if data_instances_3d is None:
        data_instances_3d = [
            InstanceData() for _ in range(len(data_instances_2d))
        ]

    for i, data_sample in enumerate(data_samples):
        data_sample.pred_instances_3d = data_instances_3d[i]
        data_sample.pred_instances = data_instances_2d[i]
    return data_samples

def stack_batch(tensor_list: List[torch.Tensor],
                pad_size_divisor: int = 1,
                pad_value: Union[int, float] = 0) -> torch.Tensor:
    """Stack multiple tensors to form a batch and pad the tensor to the max
    shape use the right bottom padding mode in these images. If
    ``pad_size_divisor > 0``, add padding to ensure the shape of each dim is
    divisible by ``pad_size_divisor``.

    Args:
        tensor_list (List[Tensor]): A list of tensors with the same dim.
        pad_size_divisor (int): If ``pad_size_divisor > 0``, add padding
            to ensure the shape of each dim is divisible by
            ``pad_size_divisor``. This depends on the model, and many
            models need to be divisible by 32. Defaults to 1
        pad_value (int, float): The padding value. Defaults to 0.

    Returns:
       Tensor: The n dim tensor.
    """
    assert isinstance(
        tensor_list,
        list), (f'Expected input type to be list, but got {type(tensor_list)}')
    assert tensor_list, '`tensor_list` could not be an empty list'
    assert len({
        tensor.ndim
        for tensor in tensor_list
    }) == 1, (f'Expected the dimensions of all tensors must be the same, '
              f'but got {[tensor.ndim for tensor in tensor_list]}')

    dim = tensor_list[0].dim()
    num_img = len(tensor_list)
    all_sizes: torch.Tensor = torch.Tensor(
        [tensor.shape for tensor in tensor_list])
    max_sizes = torch.ceil(
        torch.max(all_sizes, dim=0)[0] / pad_size_divisor) * pad_size_divisor
    padded_sizes = max_sizes - all_sizes
    # The first dim normally means channel,  which should not be padded.
    padded_sizes[:, 0] = 0
    if padded_sizes.sum() == 0:
        return torch.stack(tensor_list)
    # `pad` is the second arguments of `F.pad`. If pad is (1, 2, 3, 4),
    # it means that padding the last dim with 1(left) 2(right), padding the
    # penultimate dim to 3(top) 4(bottom). The order of `pad` is opposite of
    # the `padded_sizes`. Therefore, the `padded_sizes` needs to be reversed,
    # and only odd index of pad should be assigned to keep padding "right" and
    # "bottom".
    pad = torch.zeros(num_img, 2 * dim, dtype=torch.int)
    pad[:, 1::2] = padded_sizes[:, range(dim - 1, -1, -1)]
    batch_tensor = []
    for idx, tensor in enumerate(tensor_list):
        batch_tensor.append(
            F.pad(tensor, tuple(pad[idx].tolist()), value=pad_value))
    return torch.stack(batch_tensor)

# TODO: The data format and fields saved in json need further discussion.
#  Maybe should include model name, timestamp, filename, image info etc.
def pred2dict(data_sample: Det3DDataSample,
              min_valid_depth: float = 0.0,
              max_valid_depth: float = float("inf")) -> Dict:
    """Extract elements necessary to represent a prediction into a
    dictionary.

    It's better to contain only basic data elements such as strings and
    numbers in order to guarantee it's json-serializable.

    Args:
        data_sample (:obj:`DetDataSample`): Predictions of the model.
        pred_out_dir: Dir to save the inference results w/o
            visualization. If left as empty, no file will be saved.
            Defaults to ''.

    Returns:
        dict: Prediction results.
    """
    result = {}
    if 'pred_instances_3d' in data_sample:
        pred_instances_3d = data_sample.pred_instances_3d.numpy()
        labels_3d = pred_instances_3d.labels_3d.tolist()
        scores_3d = pred_instances_3d.scores_3d.tolist()
        bboxes_3d = pred_instances_3d.bboxes_3d.tensor.cpu().tolist()
        corners_3d = pred_instances_3d.bboxes_3d.corners.tolist()

        result = {
            'labels_3d': [],
            'scores_3d': [],
            'bboxes_3d': [],
            'corners_3d': []
        }

        for i in range(len(labels_3d)):
            depth = bboxes_3d[i][2]
            if depth > min_valid_depth and depth < max_valid_depth:
                result['labels_3d'].append(labels_3d[i])
                result['scores_3d'].append(scores_3d[i])
                result['bboxes_3d'].append(bboxes_3d[i])
                result['corners_3d'].append(corners_3d[i])
            else:
                print(f"bbox {bboxes_3d[i]} is out of [{min_valid_depth:.1f}, {max_valid_depth:.1f}]")

    result['box_type_3d'] = 'Camera'

    return result

def to_tensor(
    data: Union[torch.Tensor, np.ndarray, Sequence, int,
                float]) -> torch.Tensor:
    """Convert objects of various python types to :obj:`torch.Tensor`.

    Supported types are: :class:`numpy.ndarray`, :class:`torch.Tensor`,
    :class:`Sequence`, :class:`int` and :class:`float`.

    Args:
        data (torch.Tensor | numpy.ndarray | Sequence | int | float): Data to
            be converted.

    Returns:
        torch.Tensor: the converted data.
    """

    if isinstance(data, torch.Tensor):
        return data
    elif isinstance(data, np.ndarray):
        if data.dtype is dtype('float64'):
            data = data.astype(np.float32)
        return torch.from_numpy(data)
    elif isinstance(data, Sequence) and not mmengine.is_str(data):
        return torch.tensor(data)
    elif isinstance(data, int):
        return torch.LongTensor([data])
    elif isinstance(data, float):
        return torch.FloatTensor([data])
    else:
        raise TypeError(f'type {type(data)} cannot be converted to tensor.')

def VanishY2Pitch(vy, K):
    fy, cy = K[1][1], K[1][2]
    pitch = -np.arctan2(vy - cy, fy)
    return pitch


def Pixel2CamDepth(y, K, pitch, camH):
    fy, cy = K[1][1], K[1][2]
    v = (y - cy) / fy
    return camH * (1 + 2 * np.sin(pitch) * np.cos(pitch) * v) / (np.sin(pitch) + np.cos(pitch) * v)

def extrinsic_depth_mixing(vanish_y, pitch, K, CamHeight, point3d, debug=False):
    if (vanish_y is None and pitch is None):
        if debug:
            print("WARNING!!! No vanish_y or pitch exists, return original depth")
        return point3d[2]

    if CamHeight is None:
        if debug:
            print("WARNING!!! No CamHeight exists, return original depth")
        return point3d[2]

    if pitch is None:
        pitch = VanishY2Pitch(vanish_y, K)

    point2d = cv2.projectPoints(point3d, np.zeros(3), np.zeros(3), K, np.zeros(8))[0].squeeze()
    if debug:
        print("point2d")
        print(point2d)
    new_depth = Pixel2CamDepth(
        y=point2d[1],
        K=K,
        pitch=pitch,
        camH=CamHeight
        )
    return new_depth

def depth_post_process(dict_result, vanish_y, pitch, K, cam_h, debug=False):
    # post process depth by camera extrinsics
    for i in range(len(dict_result.get("corners_3d", []))):
        # 车尾底边中点
        rear_bottom_center_point3d = np.mean(np.array(dict_result['corners_3d'][i])[[2, 3]], axis=0)

        # 车头底边中点
        front_bottom_center_point3d = np.mean(np.array(dict_result['corners_3d'][i])[[6, 7]], axis=0)

        # 选择深度更小，更靠近相机的点，因为这样外参计算的深度更准
        if rear_bottom_center_point3d[2] > front_bottom_center_point3d[2]:
            point3d = front_bottom_center_point3d
        else:
            point3d = rear_bottom_center_point3d

        # 硬编码，限制外参估计深度的范围，单位米
        if point3d[2] < 1.0 or point3d[2] > 30.0:
            continue

        new_depth = extrinsic_depth_mixing(
            vanish_y=vanish_y,
            pitch=pitch,
            K=K,
            CamHeight=cam_h,
            point3d=point3d,
            debug=debug
        )

        # 硬编码，限制外参估计深度的范围，单位米
        if new_depth < 1.0 or new_depth > 30.0:
            continue

        # 使用新的深度修正xyz
        scale = new_depth / point3d[2]

        if debug:
            print("before extrinsic adjst")
            print(dict_result['bboxes_3d'][i])

        for j in range(3):
            for k in range(len(dict_result['corners_3d'][i])):
                dict_result['corners_3d'][i][k][j] *= scale

        for j in range(6):
            dict_result['bboxes_3d'][i][j] *= scale

        if debug:
            print("after extrinsic adjst")
            print(dict_result['bboxes_3d'][i])

    return dict_result

def depth_post_process_v2(data_sample: Det3DDataSample, vanish_y, pitch, K, cam_h, debug=False):
    """
    post process depth by camera extrinsics
    """
    new_data_sample = deepcopy(data_sample)

    pred_instances_3d = data_sample.pred_instances_3d.numpy()
    labels_3d = pred_instances_3d.labels_3d
    bboxes_3d = pred_instances_3d.bboxes_3d.tensor.cpu().numpy()
    corners_3d = pred_instances_3d.bboxes_3d.corners.cpu().numpy()

    for i in range(len(corners_3d)):
        # pedestrian
        if labels_3d[i] == 7:
            point3d = np.mean(corners_3d[i][[2, 3, 6, 7]], axis=0)
        else:
            # 车尾底边中点
            rear_bottom_center_point3d = np.mean(corners_3d[i][[2, 3]], axis=0)

            # 车头底边中点
            front_bottom_center_point3d = np.mean(corners_3d[i][[6, 7]], axis=0)

            # 选择深度更小，更靠近相机的点，因为这样外参计算的深度更准
            if rear_bottom_center_point3d[2] > front_bottom_center_point3d[2]:
                point3d = front_bottom_center_point3d
            else:
                point3d = rear_bottom_center_point3d

        # 硬编码，限制外参估计深度的范围，单位米
        if point3d[2] < 1.0 or point3d[2] > 30.0:
            continue

        new_depth = extrinsic_depth_mixing(
            vanish_y=vanish_y,
            pitch=pitch,
            K=K,
            CamHeight=cam_h,
            point3d=point3d,
            debug=debug
        )

        # 硬编码，限制外参估计深度的范围，单位米
        if new_depth < 1.0 or new_depth > 30.0:
            continue

        # 使用新的深度修正xyz
        scale = new_depth / point3d[2]

        if debug:
            print("before extrinsic adjst")
            print(bboxes_3d[i])

        for j in range(3):
            new_data_sample.pred_instances_3d.bboxes_3d.tensor[i, j] *= scale

        if debug:
            print("after extrinsic adjst")
            new_bboxes_3d = new_data_sample.pred_instances_3d.bboxes_3d.tensor.cpu().numpy()
            print(new_bboxes_3d[i])

    return new_data_sample


class OneEuroFilter:
    def __init__(self, min_cutoff=0.5, beta=20, dcutoff=100.0, freq=20.0):
        if isinstance(min_cutoff, list):
            self.min_cutoff = np.array(min_cutoff)
        else:
            self.min_cutoff = min_cutoff

        if isinstance(beta, list):
            self.beta = np.array(beta)
        else:
            self.beta = beta

        if isinstance(dcutoff, list):
            self.dcutoff = np.array(dcutoff)
        else:
            self.dcutoff = dcutoff

        self.freq = freq
        self.last_time = None
        self.x_prev = None
        self.dx_prev = None
        self.alpha_value = None

    def alpha(self, cutoff):
        # if cutoff < 1e-12:
        #     cutoff = 1e-12
        tau = 1.0 / (2.0 * math.pi * cutoff)
        te = 1.0 / self.freq
        return 1.0 / (1.0 + tau / te)

    def get_alpha_value(self):
        return self.alpha_value

    def filter(self, x, t):
        if len(x) > 6:
            # restrict yaw in [0, pi]
            x[6] -= ((x[6] // np.pi) * np.pi)

        if self.last_time is None:
            self.last_time = t
            self.x_prev = x
            self.dx_prev = 0.0

            return x

        if len(x) > 6:
            # 如果yaw变化大于90度，则增加180度的偏置，让变化小于90度
            # for example, 0 -> 176
            if x[6] - self.x_prev[6] > np.pi / 2.0:
                bias = -np.pi
                # print(f"big yaw differs, prev: {self.x_prev[6]}, curr: {x[6]}")
            # for example, 176 -> 0
            elif self.x_prev[6] - x[6] > np.pi / 2.0:
                bias = np.pi
                # print(f"big yaw differs, prev: {self.x_prev[6]}, curr: {x[6]}")
            else:
                bias = 0
            x[6] += bias

        dt = 1.0 / self.freq
        self.last_time = t

        dx = (x - self.x_prev) / dt

        alpha_d = self.alpha(self.dcutoff)

        dx_hat = alpha_d * dx + (1.0 - alpha_d) * self.dx_prev
        self.dx_prev = dx_hat

        cutoff = self.min_cutoff + self.beta * abs(dx_hat)

        alpha = self.alpha(cutoff)
        self.alpha_value = alpha

        x_hat = alpha * x + (1.0 - alpha) * self.x_prev

        if len(x_hat) > 6:
            # restrict yaw in [0, pi]
            x_hat[6] -= ((x_hat[6] // np.pi) * np.pi)

        self.x_prev = x_hat

        return x_hat
