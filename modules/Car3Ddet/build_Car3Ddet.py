from .Car3DdetModel import Car3DdetModel, Car3DdetModelPytorch
try:
    from .Car3DdetModel import Car3DdetModelTensorRT
except:
    pass

class Car3DdetWarpper:
    def __init__(self, param_setting, modules_setting, camera_path, providers, sk):
        # self.car3ddet_model = Car3DdetModel(param_setting, modules_setting, camera_path, providers, sk)
        self.car3ddet_model = Car3DdetModelPytorch(param_setting, modules_setting, camera_path, providers, sk)
        # self.car3ddet_model = Car3DdetModelTensorRT(param_setting, modules_setting, camera_path, providers, sk)

    def run(self, frame, logui_info, frame_id):
        car3ddet_result = None

        if self.car3ddet_model.enable:
            car3ddet_result = self.car3ddet_model.run(frame, logui_info, frame_id)

        return car3ddet_result
