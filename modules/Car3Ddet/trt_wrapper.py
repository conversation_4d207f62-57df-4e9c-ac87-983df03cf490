# Copyright (c) OpenMMLab. All rights reserved.
from abc import ABCMeta, abstractmethod
from typing import Any, Dict, List, Optional, Sequence, Union
import os
import ctypes

import tensorrt as trt
import torch


def load_tensorrt_plugin(lib_path) -> bool:
    """Load TensorRT plugins library.

    Returns:
        bool: True if TensorRT plugin library is successfully loaded.
    """
    success = False
    if os.path.exists(lib_path):
        ctypes.CDLL(lib_path)
        print(f'Successfully loaded tensorrt plugins from {lib_path}')
        success = True
    else:
        print(f'Could not load the library of tensorrt plugins. \
            Because the file does not exist: {lib_path}')
    return success

def load_engine(path: str, lib_path: str, allocator: Optional[Any] = None) -> trt.ICudaEngine:
    """Deserialize TensorRT engine from disk.

    Args:
        path (str): The disk path to read the engine.
        lib_path (str): The disk path to read mmploy TensorRT lib path.
        allocator (Any): gpu allocator

    Returns:
        tensorrt.ICudaEngine: The TensorRT engine loaded from disk.
    """
    load_tensorrt_plugin(lib_path)
    with trt.Logger() as logger, trt.Runtime(logger) as runtime:
        if allocator is not None:
            runtime.gpu_allocator = allocator
        with open(path, mode='rb') as f:
            engine_bytes = f.read()
        trt.init_libnvinfer_plugins(logger, namespace='')
        engine = runtime.deserialize_cuda_engine(engine_bytes)
        return engine

def torch_dtype_from_trt(dtype: trt.DataType) -> torch.dtype:
    """Convert pytorch dtype to TensorRT dtype.

    Args:
        dtype (str.DataType): The data type in tensorrt.

    Returns:
        torch.dtype: The corresponding data type in torch.
    """

    if dtype == trt.bool:
        return torch.bool
    elif dtype == trt.int8:
        return torch.int8
    elif dtype == trt.int32:
        return torch.int32
    elif dtype == trt.float16:
        return torch.float16
    elif dtype == trt.float32:
        return torch.float32
    else:
        raise TypeError(f'{dtype} is not supported by torch')


def torch_device_from_trt(device: trt.TensorLocation):
    """Convert pytorch device to TensorRT device.

    Args:
        device (trt.TensorLocation): The device in tensorrt.
    Returns:
        torch.device: The corresponding device in torch.
    """
    if device == trt.TensorLocation.DEVICE:
        return torch.device('cuda')
    elif device == trt.TensorLocation.HOST:
        return torch.device('cpu')
    else:
        return TypeError(f'{device} is not supported by torch')


class TorchAllocator(trt.IGpuAllocator):
    """PyTorch Cuda Allocator Wrapper."""

    def __init__(self, device_id: int = 0) -> None:
        super().__init__()

        self.device_id = device_id
        self.mems = set()
        self.caching_delete = torch._C._cuda_cudaCachingAllocator_raw_delete

    def __del__(self):
        """destructor."""
        mems = self.mems.copy()
        (self.deallocate(mem) for mem in mems)

    def allocate(self: trt.IGpuAllocator, size: int, alignment: int,
                 flags: int) -> int:
        """allocate gpu memory.

        Args:
            self (trt.IGpuAllocator): gpu allocator
            size (int): memory size.
            alignment (int): memory alignment.
            flags (int): flags.

        Returns:
            int: memory address.
        """
        torch_stream = torch.cuda.current_stream(self.device_id)
        print(f'allocate {size} memory with TorchAllocator.')
        assert alignment >= 0
        if alignment > 0:
            size = size | (alignment - 1) + 1
        mem = torch.cuda.caching_allocator_alloc(
            size, device=self.device_id, stream=torch_stream)
        self.mems.add(mem)
        return mem

    def deallocate(self: trt.IGpuAllocator, memory: int) -> bool:
        """deallocate memory.

        Args:
            self (trt.IGpuAllocator): gpu allocator
            memory (int): memory address.

        Returns:
            bool: deallocate success.
        """
        if memory not in self.mems:
            return False

        self.caching_delete(memory)
        self.mems.discard(memory)
        return True


class BaseWrapper(torch.nn.Module, metaclass=ABCMeta):
    """Abstract base class for backend wrappers.

    Args:
        output_names (Sequence[str]): Names to model outputs in order, which is
        useful when converting the output dict to a ordered list or converting
        the output ordered list to a key-value dict.
    """

    def __init__(self, output_names: Sequence[str]):
        super().__init__()
        self._output_names = output_names

    @staticmethod
    def get_backend_file_count() -> int:
        """Return the count of backend file(s)

        Each backend has its own requirement on backend files (e.g., TensorRT
        requires 1 .engine file and ncnn requires 2 files (.param, .bin)). This
        interface allow developers to get the count of these required files.

        Returns:
            int: The count of required backend file(s).
        """
        return 1

    @abstractmethod
    def forward(self, inputs: Dict[str,
                                   torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Run forward inference.

        Args:
            inputs (Dict[str, torch.Tensor]): Key-value pairs of model inputs.

        Returns:
            Dict[str, torch.Tensor]: Key-value pairs of model outputs.
        """
        pass

    @property
    def output_names(self):
        """Return the output names."""
        return self._output_names

    @output_names.setter
    def output_names(self, value):
        """Set the output names."""
        self._output_names = value

    def output_to_list(self, output_dict: Dict[str,
                                               torch.Tensor]) -> \
            List[torch.Tensor]:
        """Convert the output dict of forward() to a tensor list.

        Args:
            output_dict (Dict[str, torch.Tensor]): Key-value pairs of model
                outputs.

        Returns:
            List[torch.Tensor]: An output value list whose order is determined
                by the ouput_names list.
        """
        outputs = [output_dict[name] for name in self._output_names]
        return outputs


class TRTWrapper(BaseWrapper):
    """TensorRT engine wrapper for inference.

    Args:
        engine (tensorrt.ICudaEngine): TensorRT engine to wrap.
        lib_path (str): mmploy TensorRT lib path. 
        output_names (Sequence[str] | None): Names of model outputs  in order.
            Defaults to `None` and the wrapper will load the output names from
            model.

    Note:
        If the engine is converted from onnx model. The input_names and
        output_names should be the same as onnx model.

    Examples:
        >>> from mmdeploy.backend.tensorrt import TRTWrapper
        >>> engine_file = 'resnet.engine'
        >>> model = TRTWrapper(engine_file)
        >>> inputs = dict(input=torch.randn(1, 3, 224, 224))
        >>> outputs = model(inputs)
        >>> print(outputs)
    """

    def __init__(self,
                 engine: Union[str, trt.ICudaEngine],
                 lib_path: str,
                 output_names: Optional[Sequence[str]] = None,
                 device_id: int = 0):
        super().__init__(output_names)
        load_tensorrt_plugin(lib_path)
        self.engine = engine
        self.allocator = TorchAllocator(device_id)
        if isinstance(self.engine, str):
            self.engine = load_engine(engine, lib_path)

        if not isinstance(self.engine, trt.ICudaEngine):
            raise TypeError(f'`engine` should be str or trt.ICudaEngine, \
                but given: {type(self.engine)}')

        self._register_state_dict_hook(TRTWrapper.__on_state_dict)
        self.context = self.engine.create_execution_context()

        if hasattr(self.context, 'temporary_allocator'):
            self.context.temporary_allocator = self.allocator

        self.__load_io_names()

    def __load_io_names(self):
        """Load input/output names from engine."""
        names = [_ for _ in self.engine]
        input_names = list(filter(self.engine.binding_is_input, names))
        self._input_names = input_names

        if self._output_names is None:
            output_names = list(set(names) - set(input_names))
            self._output_names = output_names

    def __on_state_dict(self, state_dict: Dict[str, Any], prefix: str):
        """State dict hook
        Args:
            state_dict (Dict[str, Any]): A dict to save state information
                such as the serialized engine, input/output names.
            prefix (str): A string to be prefixed at the key of the
                state dict.
        """
        state_dict[prefix + 'engine'] = bytearray(self.engine.serialize())
        state_dict[prefix + 'input_names'] = self._input_names
        state_dict[prefix + 'output_names'] = self._output_names

    def forward(self, inputs: Dict[str,
                                   torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Run forward inference.

        Args:
            inputs (Dict[str, torch.Tensor]): The input name and tensor pairs.

        Return:
            Dict[str, torch.Tensor]: The output name and tensor pairs.
        """
        assert self._input_names is not None
        assert self._output_names is not None
        bindings = [None] * (len(self._input_names) + len(self._output_names))

        profile_id = 0
        inputs = dict((name, data.contiguous().int() if data.dtype ==
                       torch.long else data.contiguous())
                      for name, data in inputs.items())
        for input_name, input_tensor in inputs.items():
            # check if input shape is valid
            profile = self.engine.get_profile_shape(profile_id, input_name)
            assert input_tensor.dim() == len(
                profile[0]), 'Input dim is different from engine profile.'
            for s_min, s_input, s_max in zip(profile[0], input_tensor.shape,
                                             profile[2]):
                assert s_min <= s_input <= s_max, \
                    'Input shape should be between ' \
                    + f'{profile[0]} and {profile[2]}' \
                    + f' but get {tuple(input_tensor.shape)}.'
            idx = self.engine.get_binding_index(input_name)

            # All input tensors must be gpu variables
            assert 'cuda' in input_tensor.device.type
            input_tensor = input_tensor.contiguous()
            if input_tensor.dtype == torch.long:
                input_tensor = input_tensor.int()
            self.context.set_binding_shape(idx, tuple(input_tensor.shape))
            bindings[idx] = input_tensor.contiguous().data_ptr()

        # create output tensors
        outputs = {}
        for output_name in self._output_names:
            idx = self.engine.get_binding_index(output_name)
            dtype = torch_dtype_from_trt(self.engine.get_binding_dtype(idx))
            shape = tuple(self.context.get_binding_shape(idx))

            device = torch_device_from_trt(self.engine.get_location(idx))
            output = torch.empty(size=shape, dtype=dtype, device=device)
            outputs[output_name] = output
            bindings[idx] = output.data_ptr()

        self.__trt_execute(bindings=bindings)

        return outputs

    def __trt_execute(self, bindings: Sequence[int]):
        """Run inference with TensorRT.

        Args:
            bindings (list[int]): A list of integer binding the input/output.
        """
        self.context.execute_async_v2(bindings,
                                      torch.cuda.current_stream().cuda_stream)
