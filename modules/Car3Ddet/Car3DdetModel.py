import os.path as osp
from copy import deepcopy
from mmengine import dump

import time
import json 
import numpy as np 
import cv2
import onnxruntime 
from .preprocess import Det3DDataPreprocessor, LoadImageFromFileMono3D, Pack3DDetInputs, Resize
from .postprocess import fcos_predict_by_feat
from mmengine.dataset import pseudo_collate
from .utils import (
    convert_to_datasample,
    depth_post_process,
    depth_post_process_v2,
    pred2dict,
    OneEuroFilter
)
from .structures import (
    CameraInstance3DBoxes,
    Det3DDataSample,
    InstanceData,
    Box3DMode
)

from typing import Dict

import torch

try:
    from .trt_wrapper import TRTWrapper
except:
    print("Can Not import TRTWrapper. Please check if you want to use it.")


PREPROCESS_TYPE = {
    'Det3DDataPreprocessor': Det3DDataPreprocessor
}

POSTPROCESS_TYPE = {
    'FCOS3D': fcos_predict_by_feat
}


class Car3DdetModel:
    def __init__(self, param_setting, modules_setting, camera_path, providers, sk):
        
        self.car3ddetconfig = modules_setting
        self.enable = self.car3ddetconfig['enable']
        self.min_valid_depth = self.car3ddetconfig.get('min_valid_depth', 0.0)
        self.max_valid_depth = self.car3ddetconfig.get('max_valid_depth', float("inf"))
        self.debug = self.car3ddetconfig.get('debug', False)
        self.enable_track = self.car3ddetconfig.get('enable_track', False)
        self.enable_extrinsics_fix = self.car3ddetconfig.get('enable_extrinsics_fix', False)
        self.modelconfig = json.load(open(param_setting,'r'))
        device = "cuda" if "CUDAExecutionProvider" in providers else "cpu"

        self.init_camera_parameters(camera_path)
        self.init_data_loading()
        self.init_tracking()

        # build preprocess op
        for process in self.modelconfig['image_preprocess']:
            if process['op_type'] in PREPROCESS_TYPE:
                setattr(self, process['op_type'], PREPROCESS_TYPE[process['op_type']](device=device, **process['param']))

        for process in self.modelconfig['model_postprocess']:
            if process['op_type'] in POSTPROCESS_TYPE:
                setattr(self, "predict_by_feat", POSTPROCESS_TYPE[process['op_type']])
                break

        session_options = onnxruntime.SessionOptions()
        session_options.register_custom_ops_library(self.car3ddetconfig['onnxruntime_custom_op_path'])

        if sk != None:
            from cryptography.fernet import Fernet 
            f = Fernet(sk)
            car3ddet_onnx_file = f.decrypt(open(self.car3ddetconfig['model_path'],'rb').read())
            self.car3ddetmodel = onnxruntime.InferenceSession(car3ddet_onnx_file, session_options, providers=providers)
        else:
            self.car3ddetmodel = onnxruntime.InferenceSession(self.car3ddetconfig['model_path'], session_options, providers=providers)

        self.car3ddet_input_name = self.car3ddetmodel.get_inputs()[0].name
        self.car3ddet_output_names = [output.name for output in self.car3ddetmodel.get_outputs()]


    def init_camera_parameters(self, camera_path):
        self.out_cam_params = json.load(open(camera_path,'r'))['OutcarCamera']
        self.K = np.array(self.out_cam_params['CameraIntrinsics'])
        self.dist = np.array(self.out_cam_params.get('Distortion',[0.0,0,0,0,0]))
        self.cam_type = self.out_cam_params.get('Camera_type','DMS')
        self.vanish_y = self.out_cam_params.get('VanishY', None)
        self.pitch = self.out_cam_params.get('pitch', None)
        self.cam_h = self.out_cam_params.get('CameraHeight', None)  # meter
        self.img_wh = self.out_cam_params.get('ImgWH', (1920, 1080))

    def init_data_loading(self):
        self.load_pipeline = LoadImageFromFileMono3D()
        self.resize_pipeline =  Resize(scale=(1600, 900), keep_ratio=True)
        self.pack_pipeline = Pack3DDetInputs(keys=['img'])

        if self.cam_type in ['pinhole','DMS']:
            self.mapx, self.mapy = cv2.initUndistortRectifyMap(self.K, self.dist, np.zeros(3), self.K, self.img_wh, cv2.CV_32FC1)
        elif self.cam_type in ['fisheye']:
            oriK = np.array(self.out_cam_params['CameraIntrinsics_ori'])
            self.mapx, self.mapy = cv2.fisheye.initUndistortRectifyMap(oriK, self.dist, np.zeros(3), self.K, self.img_wh, cv2.CV_32FC1)

    def load_data(self, frame, logui_info):
        cur = time.time()
        if self.cam_type in ['pinhole','DMS']:
            # undistorted_image = cv2.undistort(frame, self.K, self.dist)
            undistorted_image = cv2.remap(frame, self.mapx, self.mapy, cv2.INTER_LINEAR)
            # undistorted_image = cv2.remap(frame, self.mapx, self.mapy, cv2.INTER_NEAREST)
        elif self.cam_type in ['fisheye']:
            # oriK = np.array(self.out_cam_params['CameraIntrinsics_ori'])
            # undistorted_image = cv2.fisheye.undistortImage(frame, oriK, self.dist, None, self.K)
            undistorted_image = cv2.remap(frame, self.mapx, self.mapy, cv2.INTER_LINEAR)
            # undistorted_image = cv2.remap(frame, self.mapx, self.mapy, cv2.INTER_NEAREST)
        else:
            undistorted_image = frame
        # new_camera_matrix, valid_pixels = cv2.getOptimalNewCameraMatrix(outK, outdist, img_outcar.shape[:2], 0)

        # pdb.set_trace()
        data = []
        images = {
            "CAM_OUT": {
                # "img_path": img_outcar,
                "img_path": undistorted_image,
                "cam2img": self.K,
            }
        }

        for cam_type, item in images.items():
            mono_img_info = {f'{cam_type}': item}
            data_ = dict(
                images=mono_img_info,
                box_type_3d=CameraInstance3DBoxes,
                box_mode_3d=Box3DMode.CAM)

            data_ = self.pack_pipeline(self.resize_pipeline(self.load_pipeline(data_)))
            data.append(data_)

        collate_outcar_data = pseudo_collate(data)
        logui_info['frame_outcar_undist'] = undistorted_image
        return collate_outcar_data, logui_info
        
    def run(self, frame, logui_info, frame_id):
        frame_construct, logui_info = self.load_data(frame,logui_info)
        preprocessed_data = self.preprocess(frame_construct)
        # pdb.set_trace()
        raw_model_res = self.run_model(preprocessed_data['inputs'])
        car3d_res = self.postprocess(raw_model_res, preprocessed_data['data_samples'])[0]
        valid_car3d_res = self.remove_invalid_sample(car3d_res)
        if self.enable_extrinsics_fix:
            new_car3d_res = depth_post_process_v2(
                valid_car3d_res,
                vanish_y=self.vanish_y,
                pitch=self.pitch,
                K=self.K,
                cam_h=self.cam_h,
                debug=self.debug
            )
        else:
            new_car3d_res = valid_car3d_res

        sorted_car3d_res = self.sort_sample(new_car3d_res)

        if self.enable_track:
            track_car3d_res = self.track(sorted_car3d_res, frame_id)
        else:
            track_car3d_res = new_car3d_res
        dict_result = pred2dict(track_car3d_res, self.min_valid_depth, self.max_valid_depth)

        # dict_result = depth_post_process(
        #     dict_result,
        #     vanish_y=self.vanish_y,
        #     pitch=self.pitch,
        #     K=self.K,
        #     cam_h=self.cam_h,
        #     debug=self.debug
        # )

        # out_json_path = osp.join("test_result", f'output_{frame_id:04d}.json')
        # dump(dict_result, out_json_path)

        return dict_result
    
    def preprocess(self, data):
        for process in self.modelconfig['image_preprocess']:
            if process['op_type'] in PREPROCESS_TYPE:
                return getattr(self, process['op_type'])(data)

        raise ValueError("not right image_preprocess op_type in param.json")

    def run_model(self, input_data):
        # 3d检测模型推理
        outputs = self.car3ddetmodel.run(self.car3ddet_output_names, {self.car3ddet_input_name: input_data['imgs'].detach().cpu().numpy()})

        # 重新整理多尺度的检测输出结果
        num_level = len(outputs) // 5  # 5 = 1(cls) + 1(pred) + 1(dir) + 1(attr) + 1(centerness)
        new_outputs = dict(
            cls_score=[torch.from_numpy(outputs[0*num_level + i]) for i in range(num_level)],
            bbox_pred=[torch.from_numpy(outputs[1*num_level + i]) for i in range(num_level)],
            dir_cls_pred=[
                torch.from_numpy(outputs[2*num_level + i]) for i in range(num_level)
            ],
            attr_pred=[
                torch.from_numpy(outputs[3*num_level + i]) for i in range(num_level)
            ],
            centerness=[
                torch.from_numpy(outputs[4*num_level + i]) for i in range(num_level)
            ])
        outputs = new_outputs

        return outputs

    def postprocess(self, outs: Dict, metas: Dict):
        cls_scores = outs['cls_score']
        bbox_preds = outs['bbox_pred']
        dir_cls_preds = outs['dir_cls_pred']
        attr_preds = outs['attr_pred']
        centernesses = outs['centerness']
        batch_input_metas = [data_samples.metainfo for data_samples in metas]

        data_instances_3d, _ = self.predict_by_feat(
            cls_scores=cls_scores,
            bbox_preds=bbox_preds,
            dir_cls_preds=dir_cls_preds,
            attr_preds=attr_preds,
            centernesses=centernesses,
            batch_img_metas=batch_input_metas,
            cfg=self.modelconfig['test_cfg'],
            rescale=True
        )

        data_samples = convert_to_datasample(
            data_samples=metas, data_instances_3d=data_instances_3d)

        return data_samples

    def remove_invalid_sample(self, data_sample: Det3DDataSample):
        valid_data_sample = deepcopy(data_sample)

        pred_instances_3d = data_sample.pred_instances_3d.numpy()
        labels_3d = pred_instances_3d.labels_3d
        scores_3d = pred_instances_3d.scores_3d
        bboxes_3d = pred_instances_3d.bboxes_3d.tensor.cpu().numpy()

        selected = [True] * len(labels_3d)

        for i in range(len(labels_3d)):
            is_person = labels_3d[i] == 7
            length_invalid = bboxes_3d[i][3] > 1.0
            width_invalid = bboxes_3d[i][5] > 1.0
            if is_person and (length_invalid or width_invalid):
                selected[i] = False

            is_vehicle = labels_3d[i] < 5
            length_invalid = bboxes_3d[i][3] < 2.0
            width_invalid = bboxes_3d[i][5] < 1.5
            if is_vehicle and (length_invalid or width_invalid):
                selected[i] = False

        labels_3d = labels_3d[selected]
        scores_3d = scores_3d[selected]
        bboxes_3d = bboxes_3d[selected]

        results = InstanceData()
        results.bboxes_3d = CameraInstance3DBoxes(
            torch.from_numpy(np.array(bboxes_3d)),
            box_dim=9,
            origin=(0.5, 1.0, 0.5)
        )
        results.scores_3d = torch.from_numpy(np.array(scores_3d))
        results.labels_3d = torch.from_numpy(np.array(labels_3d))

        valid_data_sample.pred_instances_3d = results

        return valid_data_sample

    def sort_sample(self, data_sample: Det3DDataSample):
        sorted_data_sample = deepcopy(data_sample)

        pred_instances_3d = data_sample.pred_instances_3d.numpy()
        labels_3d = pred_instances_3d.labels_3d
        scores_3d = pred_instances_3d.scores_3d
        bboxes_3d = pred_instances_3d.bboxes_3d.tensor.cpu().numpy()

        vehicle_indices = []
        person_indices = []
        other_indices = []

        for i in range(len(labels_3d)):
            # vehicle
            if labels_3d[i] < 5:
                vehicle_indices.append(i)
            # person
            elif labels_3d[i] < 8:
                person_indices.append(i)
            # others
            else:
                other_indices.append(i)

        vehicle_indices_sorted = sorted(vehicle_indices, key=lambda i: bboxes_3d[i][0] / bboxes_3d[i][2])
        person_indices_sorted = sorted(person_indices, key=lambda i: bboxes_3d[i][0] / bboxes_3d[i][2])
        other_indices_sorted = sorted(other_indices, key=lambda i: bboxes_3d[i][0] / bboxes_3d[i][2])

        indices_sorted = vehicle_indices_sorted + person_indices_sorted + other_indices_sorted

        labels_3d = labels_3d[indices_sorted]
        scores_3d = scores_3d[indices_sorted]
        bboxes_3d = bboxes_3d[indices_sorted]

        # debug
        # print(labels_3d)
        # print([bboxes_3d[i][0] / bboxes_3d[i][2] for i in range(len(bboxes_3d))])

        results = InstanceData()
        results.bboxes_3d = CameraInstance3DBoxes(
            torch.from_numpy(np.array(bboxes_3d)),
            box_dim=9,
            origin=(0.5, 1.0, 0.5)
        )
        results.scores_3d = torch.from_numpy(np.array(scores_3d))
        results.labels_3d = torch.from_numpy(np.array(labels_3d))

        sorted_data_sample.pred_instances_3d = results

        return sorted_data_sample

    def init_tracking(self):
        self.all_map_type = ['car', 'person']
        self.latest_track_id = {map_type: 0 for map_type in self.all_map_type}
        self.tracking_objects = {map_type: {} for map_type in self.all_map_type}
        self.max_lifetime = 10  # frame
        self.match_max_distance = 2.0  # meter

        """
        'categories': {
            'car': 0,
            'truck': 1,
            'trailer': 2,
            'bus': 3,
            'construction_vehicle': 4,
            'bicycle': 5,
            'motorcycle': 6,
            'pedestrian': 7,
            'traffic_cone': 8,
            'barrier': 9
        }
        """
        self.label2map_type = {
            0: "car",
            1: "car",
            2: "car",
            3: "car",
            4: "car",
            5: "person",
            6: "person",
            7: "person"
        }

    def create_tracking_object(self, map_type, label, bbox_3d, frame_id):
        track_id = self.latest_track_id.pop(map_type)
        self.tracking_objects[map_type][track_id] = {
            'label': label,
            'bbox_3d': bbox_3d,
            'filters': OneEuroFilter(
                min_cutoff=2.0,
                dcutoff=1.0,
                freq=20,
                beta=[0.5, 0.01, 0.5]  # xyz
                    + [0.001, 0.001, 0.001]  # lenght, height, width
                    + [0.1, 0.01, 0.01]  # yaw(rad), vx, vy
            )
        }
        self.tracking_objects[map_type][track_id]['filters'].filter(bbox_3d, frame_id)
        self.latest_track_id[map_type] = track_id + 1

    def track(self, data_sample: Det3DDataSample, frame_id: int):
        new_data_sample = deepcopy(data_sample)
        pred_instances_3d = data_sample.pred_instances_3d.numpy()
        labels_3d = pred_instances_3d.labels_3d
        bboxes_3d = pred_instances_3d.bboxes_3d.tensor.cpu().numpy()
        unmatch_track_id = {map_type: set() for map_type in self.all_map_type}

        # remove lost object
        for map_type in self.all_map_type:
            track_ids = list(self.tracking_objects[map_type].keys())
            for track_id in track_ids:
                if frame_id - self.tracking_objects[map_type][track_id]['filters'].last_time > self.max_lifetime:
                    self.tracking_objects[map_type].pop(track_id)
                    print(f"remove {map_type}: track_id: {track_id} due to long-time disappearance")
                else:
                    unmatch_track_id[map_type].add(track_id)

        if self.debug:
            from pprint import pprint
            print(f"frame_id: {frame_id}")
            pprint(self.tracking_objects)

        for det_idx in range(len(labels_3d)):
            label = labels_3d[det_idx]

            # skip irrelevant samples
            if label not in self.label2map_type.keys():
                continue

            map_type = self.label2map_type[label]
            bbox_3d = bboxes_3d[det_idx]
            det_center = bbox_3d[:3]

            # match by distance from unmatched tracking objects
            min_dist = float("inf")
            match_track_id = None
            for track_id in unmatch_track_id[map_type]:
                obj = self.tracking_objects[map_type][track_id]
                obj_center = obj['bbox_3d'][:3]
                dist = np.linalg.norm(det_center - obj_center)
                if dist < min_dist:
                    min_dist = dist
                    if dist < self.match_max_distance:
                        match_track_id = track_id

            # update tracking result
            if match_track_id is not None:
                # remove track id
                unmatch_track_id[map_type].remove(match_track_id)
                # update tracking result
                self.tracking_objects[map_type][match_track_id]['filters'].filter(bbox_3d, frame_id)
                # update det result
                new_data_sample.pred_instances_3d.bboxes_3d.tensor[det_idx] = \
                    torch.from_numpy(self.tracking_objects[map_type][match_track_id]['filters'].x_prev)

                if self.debug:
                    print(f"map_type: {map_type}")
                    print(f"\ttrack_id: {match_track_id}")
                    print("\t\tdet")
                    print(bbox_3d)
                    print("\t\ttrack")
                    print(self.tracking_objects[map_type][match_track_id]['filters'].x_prev)

            # create new object
            else:
                self.create_tracking_object(map_type, label, bbox_3d, frame_id)

        tracked_data_sample = deepcopy(new_data_sample)
        pred_instances_3d = new_data_sample.pred_instances_3d.numpy()
        labels_3d = pred_instances_3d.labels_3d.tolist()
        scores_3d = pred_instances_3d.scores_3d.tolist()
        bboxes_3d = pred_instances_3d.bboxes_3d.tensor.cpu().tolist()

        for map_type in self.all_map_type:
            for track_id in unmatch_track_id[map_type]:
                tracking_object = self.tracking_objects[map_type][track_id]
                # if self.debug:
                #     print("add object fn from tracking")
                #     print(f"map_type: {map_type}")
                #     print(f"\ttrack_id: {track_id}")
                #     print(tracking_object)
                # labels_3d.append(tracking_object['label'])
                # scores_3d.append(0.0)  # fn, retrived from track
                # bboxes_3d.append(tracking_object['bbox_3d'])


        # add fn from tracking object
        results = InstanceData()
        results.bboxes_3d = CameraInstance3DBoxes(
            torch.from_numpy(np.array(bboxes_3d)),
            box_dim=9,
            origin=(0.5, 1.0, 0.5)
        )
        results.scores_3d = torch.from_numpy(np.array(scores_3d))
        results.labels_3d = torch.from_numpy(np.array(labels_3d))

        tracked_data_sample.pred_instances_3d = results

        return tracked_data_sample


class Car3DdetModelPytorch(Car3DdetModel):
    def __init__(self, param_setting, modules_setting, camera_path, providers, sk):
        device = "cuda" if "CUDAExecutionProvider" in providers else "cpu"
        self.device = device

        self.car3ddetconfig = modules_setting
        self.enable = self.car3ddetconfig['enable']
        self.min_valid_depth = self.car3ddetconfig.get('min_valid_depth', 0.0)
        self.max_valid_depth = self.car3ddetconfig.get('max_valid_depth', float("inf"))
        self.debug = self.car3ddetconfig.get('debug', False)
        self.enable_track = self.car3ddetconfig.get('enable_track', False)
        self.enable_extrinsics_fix = self.car3ddetconfig.get('enable_extrinsics_fix', False)
        self.modelconfig = json.load(open(param_setting,'r'))

        self.init_camera_parameters(camera_path)
        self.init_data_loading()
        self.init_tracking()

        # build preprocess op
        for process in self.modelconfig['image_preprocess']:
            if process['op_type'] in PREPROCESS_TYPE:
                setattr(self, process['op_type'], PREPROCESS_TYPE[process['op_type']](device=device, **process['param']))

        for process in self.modelconfig['model_postprocess']:
            if process['op_type'] in POSTPROCESS_TYPE:
                setattr(self, "predict_by_feat", POSTPROCESS_TYPE[process['op_type']])
                break

        self.model = torch.load(self.car3ddetconfig['pytorch_model_path'])
        # self.model = torch.load("data/models/Car3Ddet/MMDet3d_fcos3d.pt")
        self.model.eval()
        self.model.to(device)
        # dummy forward warmup
        frame_construct, _ = self.load_data(np.random.randn(self.img_wh[1], self.img_wh[0], 3), {})
        preprocessed_data = self.preprocess(frame_construct)
        raw_model_res = self.run_model(preprocessed_data['inputs'])
        _ = self.postprocess(raw_model_res, preprocessed_data['data_samples'])[0]

        # self.model({"imgs": torch.randn([1, 3, 928, 1600], dtype=torch.float32, device=device)})


    def run_model(self, input_data):
        # 3d检测模型推理
        # outputs = self.car3ddetmodel.run(self.car3ddet_output_names, {self.car3ddet_input_name: input_data['imgs'].detach().cpu().numpy()})
        if self.device == "cuda":
            torch.cuda.synchronize()
        start = time.time()
        with torch.no_grad():
            outputs = self.model(input_data)
        if self.device == "cuda":
            torch.cuda.synchronize()
        end = time.time()
        if self.debug:
            print(f"run: {end - start}s")

        # start = time.time()
        # 重新整理多尺度的检测输出结果
        num_level = len(outputs[0])  # 5 = 1(cls) + 1(pred) + 1(dir) + 1(attr) + 1(centerness)
        new_outputs = dict(
            cls_score=[outputs[0][i] for i in range(num_level)],
            bbox_pred=[outputs[1][i] for i in range(num_level)],
            dir_cls_pred=[
                outputs[2][i] for i in range(num_level)
            ],
            attr_pred=[
                outputs[3][i] for i in range(num_level)
            ],
            centerness=[
                outputs[4][i] for i in range(num_level)
            ])
        outputs = new_outputs
        # end = time.time()
        # print(f"nms: {end - start}")
        return outputs

class Car3DdetModelTensorRT(Car3DdetModel):
    def __init__(self, param_setting, modules_setting, camera_path, providers, sk):
        assert torch.cuda.is_available(), "TensorRT model must run in GPUs"
        device_id = 0
        self.device = f"cuda:{device_id}"

        self.car3ddetconfig = modules_setting
        self.enable = self.car3ddetconfig['enable']
        self.min_valid_depth = self.car3ddetconfig.get('min_valid_depth', 0.0)
        self.max_valid_depth = self.car3ddetconfig.get('max_valid_depth', float("inf"))
        self.debug = self.car3ddetconfig.get('debug', False)
        self.enable_track = self.car3ddetconfig.get('enable_track', False)
        self.enable_extrinsics_fix = self.car3ddetconfig.get('enable_extrinsics_fix', False)
        self.modelconfig = json.load(open(param_setting,'r'))

        self.init_camera_parameters(camera_path)
        self.init_data_loading()
        self.init_tracking()

        # build preprocess op
        for process in self.modelconfig['image_preprocess']:
            if process['op_type'] in PREPROCESS_TYPE:
                setattr(self, process['op_type'], PREPROCESS_TYPE[process['op_type']](device=self.device, **process['param']))

        for process in self.modelconfig['model_postprocess']:
            if process['op_type'] in POSTPROCESS_TYPE:
                setattr(self, "predict_by_feat", POSTPROCESS_TYPE[process['op_type']])
                break

        engine_path = self.car3ddetconfig['trt_model_path']
        lib_path = self.car3ddetconfig['tensorrt_custom_op_path']
        output_names = [
            'cls_score0', 'cls_score1', 'cls_score2', 'cls_score3', 'cls_score4',
            'bbox_pred0', 'bbox_pred1', 'bbox_pred2', 'bbox_pred3', 'bbox_pred4',
            'dir_cls_pred0', 'dir_cls_pred1', 'dir_cls_pred2', 'dir_cls_pred3', 'dir_cls_pred4',
            'attr_pred0', 'attr_pred1', 'attr_pred2', 'attr_pred3', 'attr_pred4',
            'centerness0', 'centerness1', 'centerness2', 'centerness3', 'centerness4'
        ]
        self.model = TRTWrapper(engine_path, lib_path, output_names, device_id)
        # warmup
        frame_construct, _ = self.load_data(np.random.randn(self.img_wh[1], self.img_wh[0], 3), {})
        preprocessed_data = self.preprocess(frame_construct)
        raw_model_res = self.run_model(preprocessed_data['inputs'])
        _ = self.postprocess(raw_model_res, preprocessed_data['data_samples'])[0]
        # self.model({"input": torch.randn((1, 3, 928, 1216), dtype=torch.float32, device=self.device)})


    def run_model(self, input_data):
        # 3d检测模型推理
        # outputs = self.car3ddetmodel.run(self.car3ddet_output_names, {self.car3ddet_input_name: input_data['imgs'].detach().cpu().numpy()})
        if self.debug:
            torch.cuda.synchronize()
            start = time.time()
        outputs = self.model({"input": input_data['imgs']})
        if self.debug:
            torch.cuda.synchronize()
            end = time.time()
        if self.debug:
            print(f"run: {end - start}s")

        # start = time.time()
        # 重新整理多尺度的检测输出结果
        num_level = len(outputs) // 5  # 5 = 1(cls) + 1(pred) + 1(dir) + 1(attr) + 1(centerness)
        new_outputs = dict(
            cls_score=[outputs[f'cls_score{i}'] for i in range(num_level)],
            bbox_pred=[outputs[f'bbox_pred{i}'] for i in range(num_level)],
            dir_cls_pred=[
                outputs[f'dir_cls_pred{i}'] for i in range(num_level)
            ],
            attr_pred=[
                outputs[f'attr_pred{i}'] for i in range(num_level)
            ],
            centerness=[
                outputs[f'centerness{i}'] for i in range(num_level)
            ])
        outputs = new_outputs

        return outputs
