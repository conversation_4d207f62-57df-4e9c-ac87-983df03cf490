# Copyright (c) OpenMMLab. All rights reserved.

from typing import List, Optional, <PERSON><PERSON>

import numpy as np
import torch
from torch import Tensor

from .structures import (
    FCOS3DBBoxCoder, InstanceData, InstanceList,
    xywhr2xyxyr, xywhr2xyxyr_with_cls,
    points_img2cam, box3d_multiclass_nms
)


# TODO: Refactor using MlvlPointGenerator in MMDet.
def get_points(featmap_sizes: List[Tuple[int]],
               strides: List[int],
               dtype: torch.dtype,
               device: torch.device,
               flatten: bool = False) -> List[Tuple[Tensor, Tensor]]:
    """Get points according to feature map sizes.

    Args:
        featmap_sizes (list[tuple]): Multi-level feature map sizes.
        dtype (torch.dtype): Type of points.
        device (torch.device): Device of points.
        flatten (bool): Whether to flatten the tensor.
            Defaults to False.

    Returns:
        list[tuple]: points of each image.
    """
    def _get_points_single(featmap_size: Tuple[int],
                           stride: int,
                           dtype: torch.dtype,
                           device: torch.device,
                           flatten: bool = False) -> Tensor:
        """Get points of a single scale level.

        Args:
            featmap_size (tuple[int]): Single scale level feature map size.
            stride (int): Downsample factor of the feature map.
            dtype (torch.dtype): Type of points.
            device (torch.device): Device of points.
            flatten (bool): Whether to flatten the tensor.
                Defaults to False.

        Returns:
            Tensor: points of each image.
        """
        h, w = featmap_size
        x_range = torch.arange(w, dtype=dtype, device=device)
        y_range = torch.arange(h, dtype=dtype, device=device)
        y, x = torch.meshgrid(y_range, x_range)
        if flatten:
            y = y.flatten()
            x = x.flatten()

        points = torch.stack((x.reshape(-1) * stride, y.reshape(-1) * stride),
                             dim=-1) + stride // 2
        return points

    mlvl_points = []
    for i in range(len(featmap_sizes)):
        mlvl_points.append(
            _get_points_single(featmap_sizes[i], strides[i],
                                        dtype, device, flatten))
    return mlvl_points

def fcos_predict_by_feat(
    cls_scores: List,
    bbox_preds: List,
    dir_cls_preds: List,
    attr_preds: List,
    centernesses: List,
    batch_img_metas: Optional[List[dict]] = None,
    cfg: dict = None,
    rescale: bool = False) -> InstanceList:
    """Transform network output for a batch into bbox predictions.

    Args:
        cls_scores (list): Box scores for each scale level
            Has shape (N, num_points * num_classes, H, W)
        bbox_preds (list): Box energies / deltas for each scale
            level with shape (N, num_points * 4, H, W)
        dir_cls_preds (list): Box scores for direction class
            predictions on each scale level, each is a 4D-tensor,
            the channel number is num_points * 2. (bin = 2)
        attr_preds (list): Attribute scores for each scale level
            Has shape (N, num_points * num_attrs, H, W)
        centernesses (list): Centerness for each scale level with
            shape (N, num_points * 1, H, W)
        batch_img_metas (list[dict]): Meta information of each image, e.g.,
            image size, scaling factor, etc.
        cfg (ConfigDict, optional): Test / postprocessing
            configuration, if None, test_cfg would be used.
            Defaults to None.
        rescale (bool): If True, return boxes in original image space.
            Defaults to False.

    Returns:
        list[:obj:`InstanceData`]: Object detection results of each image
        after the post process. Each item usually contains following keys.

            - scores_3d (Tensor): Classification scores, has a shape
              (num_instance, )
            - labels_3d (Tensor): Labels of bboxes, has a shape
              (num_instances, ).
            - bboxes_3d (Tensor): Contains a tensor with shape
              (num_instances, C), where C >= 7.
    """
    assert len(cls_scores) == len(bbox_preds) == len(dir_cls_preds) == \
        len(centernesses) == len(attr_preds)
    num_levels = len(cls_scores)

    cls_out_channels = cfg['num_classes']
    use_direction_classifier = cfg['use_direction_classifier']
    bbox_code_size = 9  # For nuscenes
    pred_attrs = cfg['pred_attrs']
    num_attrs = 9  # For nuscenes
    strides = cfg['strides']
    group_reg_dims = cfg['group_reg_dims']
    dir_offset = cfg['dir_offset']
    attr_background_label = -1
    if pred_attrs:
        attr_background_label = num_attrs

    def _predict_by_feat_single(
        cls_score_list: List,
        bbox_pred_list: List,
        dir_cls_pred_list: List,
        attr_pred_list: List,
        centerness_pred_list: List,
        mlvl_points: List,
        img_meta: dict,
        cfg: dict,
        rescale: bool = False) -> InstanceData:
        """Transform outputs for a single batch item into bbox predictions.

        Args:
            cls_scores (list): Box scores for a single scale level
                Has shape (num_points * num_classes, H, W).
            bbox_preds (list): Box energies / deltas for a single scale
                level with shape (num_points * bbox_code_size, H, W).
            dir_cls_preds (list): Box scores for direction class
                predictions on a single scale level with shape
                (num_points * 2, H, W)
            attr_preds (list): Attribute scores for each scale level
                Has shape (N, num_points * num_attrs, H, W)
            centernesses (list): Centerness for a single scale level
                with shape (num_points, H, W).
            mlvl_points (list): Box reference for a single scale level
                with shape (num_total_points, 2).
            img_meta (dict): Metadata of input image.
            cfg (mmengine.Config): Test / postprocessing configuration,
                if None, test_cfg would be used.
            rescale (bool): If True, return boxes in original image space.

        Returns:
            :obj:`InstanceData`: 3D Detection results of each image
            after the post process.
            Each item usually contains following keys.

                - scores_3d (Tensor): Classification scores, has a shape
                  (num_instance, )
                - labels_3d (Tensor): Labels of bboxes, has a shape
                  (num_instances, ).
                - bboxes_3d (Tensor): Contains a tensor with shape
                  (num_instances, C), where C >= 7.
        """
        view = np.array(img_meta['cam2img'])
        scale_factor = img_meta['scale_factor']
        # print("scale_factor")
        # print(f"{scale_factor}")
        # depth_factor = 0.5 * (view[0, 0] + view[1, 1]) / 1250.0  # new_focal_length / ref_focal_length
        # depth_factor = scale_factor[0]
        # resize的等效焦距放缩 * 新老焦距比例
        depth_factor = scale_factor[0] * 0.5 * (view[0, 0] + view[1, 1]) / 1250.0  # new_focal_length / ref_focal_length
        assert len(cls_score_list) == len(bbox_pred_list) == len(mlvl_points)
        mlvl_centers_2d = []
        mlvl_bboxes = []
        mlvl_scores = []
        mlvl_dir_scores = []
        mlvl_attr_scores = []
        mlvl_centerness = []

        bbox_coder = FCOS3DBBoxCoder(code_size=bbox_code_size)

        for cls_score, bbox_pred, dir_cls_pred, attr_pred, centerness, \
                points in zip(cls_score_list, bbox_pred_list,
                              dir_cls_pred_list, attr_pred_list,
                              centerness_pred_list, mlvl_points):
            assert cls_score.size()[-2:] == bbox_pred.size()[-2:]
            scores = cls_score.permute(1, 2, 0).reshape(
                -1, cls_out_channels).sigmoid()
            dir_cls_pred = dir_cls_pred.permute(1, 2, 0).reshape(-1, 2)
            dir_cls_score = torch.max(dir_cls_pred, dim=-1)[1]
            attr_pred = attr_pred.permute(1, 2, 0).reshape(-1, num_attrs)
            attr_score = torch.max(attr_pred, dim=-1)[1]
            centerness = centerness.permute(1, 2, 0).reshape(-1).sigmoid()

            bbox_pred = bbox_pred.permute(1, 2,
                                          0).reshape(-1,
                                                     sum(group_reg_dims))
            bbox_pred = bbox_pred[:, :bbox_code_size]
            nms_pre = cfg.get('nms_pre', -1)
            if nms_pre > 0 and scores.shape[0] > nms_pre:
                max_scores, _ = (scores * centerness[:, None]).max(dim=1)
                _, topk_inds = max_scores.topk(nms_pre)
                points = points[topk_inds, :]
                bbox_pred = bbox_pred[topk_inds, :]
                scores = scores[topk_inds, :]
                dir_cls_pred = dir_cls_pred[topk_inds, :]
                centerness = centerness[topk_inds]
                dir_cls_score = dir_cls_score[topk_inds]
                attr_score = attr_score[topk_inds]
            # change the offset to actual center predictions
            bbox_pred[:, :2] = points - bbox_pred[:, :2]
            if rescale:
                bbox_pred[:, :2] /= bbox_pred[:, :2].new_tensor(scale_factor)
                # rescale depth
                bbox_pred[:, 2:3] *= bbox_pred[:, 2:3].new_tensor(depth_factor)
            pred_center2d = bbox_pred[:, :3].clone()
            bbox_pred[:, :3] = points_img2cam(bbox_pred[:, :3], view)
            mlvl_centers_2d.append(pred_center2d)
            mlvl_bboxes.append(bbox_pred)
            mlvl_scores.append(scores)
            mlvl_dir_scores.append(dir_cls_score)
            mlvl_attr_scores.append(attr_score)
            mlvl_centerness.append(centerness)

        mlvl_centers_2d = torch.cat(mlvl_centers_2d)
        mlvl_bboxes = torch.cat(mlvl_bboxes)
        mlvl_dir_scores = torch.cat(mlvl_dir_scores)

        # change local yaw to global yaw for 3D nms
        cam2img = mlvl_centers_2d.new_zeros((4, 4))
        cam2img[:view.shape[0], :view.shape[1]] = \
            mlvl_centers_2d.new_tensor(view)
        mlvl_bboxes = bbox_coder.decode_yaw(mlvl_bboxes, mlvl_centers_2d,
                                            mlvl_dir_scores,
                                            dir_offset, cam2img)

        # mlvl_bboxes_for_nms = xywhr2xyxyr(img_meta['box_type_3d'](
        #     mlvl_bboxes, box_dim=bbox_code_size,
        #     origin=(0.5, 0.5, 0.5)).bev)

        mlvl_scores = torch.cat(mlvl_scores)
        padding = mlvl_scores.new_zeros(mlvl_scores.shape[0], 1)
        # remind that we set FG labels to [0, num_class-1] since mmdet v2.0
        # BG cat_id: num_class
        mlvl_scores = torch.cat([mlvl_scores, padding], dim=1)
        mlvl_attr_scores = torch.cat(mlvl_attr_scores)
        mlvl_centerness = torch.cat(mlvl_centerness)
        # no scale_factors in box3d_multiclass_nms
        # Then we multiply it from outside
        mlvl_nms_scores = mlvl_scores * mlvl_centerness[:, None]
        scale = np.array([
                1.0,  # 'car'
                1.0,  # 'truck'
                1.0,  # 'trailer'
                1.0,  # 'bus'
                1.0,  # 'construction_vehicle'
                2.0,  # 'bicycle'
                2.0,  # 'motorcycle'
                4.0,  # 'pedestrian'
                8.0,  # 'traffic_cone'
                2.0,  # 'barrier'
                1.0,  # 'background'
            ])

        if torch.is_tensor(mlvl_nms_scores):
            scale = mlvl_nms_scores.new_tensor(scale)

        mlvl_bboxes_for_nms = xywhr2xyxyr_with_cls(
            boxes_xywhr=img_meta['box_type_3d'](
                mlvl_bboxes,
                box_dim=bbox_code_size,
                origin=(0.5, 0.5, 0.5)
            ).bev,
            scores=mlvl_nms_scores,
            scale=scale
        )

        results = box3d_multiclass_nms(mlvl_bboxes, mlvl_bboxes_for_nms,
                                       mlvl_nms_scores, cfg['score_thr'],
                                       cfg['max_per_img'], cfg, mlvl_dir_scores,
                                       mlvl_attr_scores)
        bboxes, scores, labels, dir_scores, attrs = results
        attrs = attrs.to(labels.dtype)  # change data type to int
        bboxes = img_meta['box_type_3d'](
            bboxes, box_dim=bbox_code_size, origin=(0.5, 0.5, 0.5))
        # Note that the predictions use origin (0.5, 0.5, 0.5)
        # Due to the ground truth centers_2d are the gravity center of objects
        # v0.10.0 fix inplace operation to the input tensor of cam_box3d
        # So here we also need to add origin=(0.5, 0.5, 0.5)

        results = InstanceData()
        results.bboxes_3d = bboxes
        results.scores_3d = scores
        results.labels_3d = labels
        if pred_attrs and attrs is not None:
            results.attr_labels = attrs

        return results

    featmap_sizes = [featmap.size()[-2:] for featmap in cls_scores]
    # TODO: refactor using prior_generator
    mlvl_points = get_points(featmap_sizes, strides,
                             bbox_preds[0].dtype,
                             bbox_preds[0].device)
    result_list = []
    for img_id in range(len(batch_img_metas)):
        img_meta = batch_img_metas[img_id]
        cls_score_list = select_single_mlvl(cls_scores, img_id)
        bbox_pred_list = select_single_mlvl(bbox_preds, img_id)

        if use_direction_classifier:
            dir_cls_pred_list = select_single_mlvl(dir_cls_preds, img_id)
        else:
            dir_cls_pred_list = [
                cls_scores[i][img_id].new_full(
                    [2, *cls_scores[i][img_id].shape[1:]], 0).detach()
                for i in range(num_levels)
            ]

        if pred_attrs:
            attr_pred_list = select_single_mlvl(attr_preds, img_id)
        else:
            attr_pred_list = [
                cls_scores[i][img_id].new_full(
                    [num_attrs, *cls_scores[i][img_id].shape[1:]],
                    attr_background_label).detach()
                for i in range(num_levels)
            ]

        centerness_pred_list = select_single_mlvl(centernesses, img_id)
        results = _predict_by_feat_single(
            cls_score_list=cls_score_list,
            bbox_pred_list=bbox_pred_list,
            dir_cls_pred_list=dir_cls_pred_list,
            attr_pred_list=attr_pred_list,
            centerness_pred_list=centerness_pred_list,
            mlvl_points=mlvl_points,
            img_meta=img_meta,
            cfg=cfg,
            rescale=rescale)
        result_list.append(results)
    result_list_2d = None
    return result_list, result_list_2d

def select_single_mlvl(mlvl_tensors, batch_id, detach=True):
    """Extract a multi-scale single image tensor from a multi-scale batch
    tensor based on batch index.

    Note: The default value of detach is True, because the proposal gradient
    needs to be detached during the training of the two-stage model. E.g
    Cascade Mask R-CNN.

    Args:
        mlvl_tensors (list): Batch tensor for all scale levels,
           each is a 4D-tensor.
        batch_id (int): Batch index.
        detach (bool): Whether detach gradient. Default True.

    Returns:
        list: Multi-scale single image tensor.
    """
    assert isinstance(mlvl_tensors, (list, tuple))
    num_levels = len(mlvl_tensors)

    if detach:
        mlvl_tensor_list = [
            mlvl_tensors[i][batch_id].detach() for i in range(num_levels)
        ]
    else:
        mlvl_tensor_list = [
            mlvl_tensors[i][batch_id] for i in range(num_levels)
        ]
    return mlvl_tensor_list
