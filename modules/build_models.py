from .Car3Ddet.build_Car3Ddet import Car3DdetWarpper
from .GazeCar.build_GazeCar import GazeCarWarpper
import pdb
import numpy as np
# pdb.set_trace()
# from algorithmworkflow.modules.Gaze.GazeModel import GazeModel
# from algorithmworkflow.modules.WholeBody.build_wholebody import WholebodyWarpper
# from algorithmworkflow.modules.HeadposeAlign.HeadposeAlignModel import HeadposeAlignModel
# from algorithmworkflow.modules.Head3d.build_head3d import Head3dWarpper

import cv2
import json
import time
import pickle

class CustomJSONEncoder(json.JSONEncoder):
    def encode(self, obj):
        if isinstance(obj, dict):
            result = "{\n"
            for i, (key, value) in enumerate(obj.items()):
                key_str = json.dumps(key)
                value_str = self.encode(value)
                result += f"    {key_str}: {value_str}"
                if i < len(obj) - 1:
                    result += ",\n"
                else:
                    result += "\n"
            result += "}"
            return result
        elif isinstance(obj, list):
            result = "[\n"
            for i, item in enumerate(obj):
                item_str = self.encode(item)
                result += f"    {item_str}"
                if i < len(obj) - 1:
                    result += ",\n"
                else:
                    result += "\n"
            result += "]"
            return result
        else:
            return super().encode(obj)

class ModelsWarpper:
    def __init__(self, param_setting_path, modules_setting_path, camera_path, provider, sk=b'lqMJHOk-8Hy_LzbD8-GcMH9aCuzhlLnvaQbh8YCUDQw='):
        self.camera_path = camera_path
        self.param_setting = json.load(open(param_setting_path, "r"))
        self.modules_setting = json.load(open(modules_setting_path, "r"))
        if not self.modules_setting.get("encrypt", False):
            sk = None
        self.car3Ddet_module = Car3DdetWarpper(self.param_setting["Car3Ddet"], self.modules_setting["Car3Ddet"], self.camera_path, provider, sk)
        self.gazecar_module = GazeCarWarpper(self.modules_setting["GazeCar"], self.camera_path)
        json.dump(json.load(open(self.camera_path, "r"))['IncarCamera'],open('temp_cam.json','w'))
        IncarCamera = 'temp_cam.json'
        #self.wholebody_module = WholebodyWarpper(self.modules_setting["Wholebody"], provider, sk)
        # self.headpose_module = HeadposeAlignModel(self.param_setting["HeadposeAlign"], self.modules_setting["HeadposeAlign"],  IncarCamera, provider, sk)
        # self.head3d_module = Head3dWarpper(self.param_setting["Head3d"], self.modules_setting["Head3d"], IncarCamera)
        # self.gaze_module = GazeModel(self.param_setting["Gaze"], self.modules_setting["Gaze"], IncarCamera, provider, sk)
        self.frame_id = -1
        
    def run_gazevec(self, gaze_input, frame_outcar, cur_time, logui_info={},gaze_input_info={}):
        logui_info['frame_outcar'] = frame_outcar
        cur_time = time.time()
        self.frame_id += 1
        # pdb.set_trace()
        self.car_bboxes = self.car3Ddet_module.run(frame_outcar, logui_info, self.frame_id)
        logui_info['car_bboxes_res'] = self.car_bboxes
        print('car_bboxes:',time.time()-cur_time); cur_time = time.time()
        if len(self.car_bboxes.get('corners_3d',[])) == 0:
            logui_info['car_bboxes'] = False
        else:
            logui_info['Boxes'] = np.array(self.car_bboxes['corners_3d'])*1000
            logui_info['car_bboxes'] = True

        # self.face_bbox = self.wholebody_module.run(frame_incar, logui_info)
        # if self.face_bbox is None:
        #     logui_info['face_bbox'] = False
        # else:
        #     logui_info['face_bbox'] = True

        if not logui_info['car_bboxes']:
            self.logui_info = logui_info
            return 
            # return logui_info

        # face_bbox = [int(round(i)) for i in self.face_bbox]
        # h,w,_ = frame_incar.shape
        # faceimg = frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],0]
        # smp = {}
        # smp['face_bbox'] = face_bbox
        # smp['faceimg'] = faceimg.tolist()
        # smp['img_h'] = h
        # smp['img_w'] = w
        cur_time = time.time()
        local_time = time.localtime(cur_time)
        seconds = int(cur_time)
        milliseconds = int((cur_time - seconds) * 1000)
        formatted_time = time.strftime("%Y%m%d%H%M%S", local_time)+f".{milliseconds:03d}"
        # gaze_input_info[formatted_time] = smp
        # pdb.set_trace() 
        # json.dump(gaze_input_info, open('gaze_input_info_face_seq.json','w'),indent=4)
        # gaze_input_info = json.load(open('gaze_input_info_face.json','r'))
        # face_bbox = gaze_input_info['face_bbox']
        # faceimg = np.array(gaze_input_info['faceimg'])
        # frame_incar = np.ones((gaze_input_info['img_h'],gaze_input_info['img_w'],3))*120
        # frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],0] = faceimg
        # frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],1] = faceimg
        # frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],2] = faceimg
        # frame_incar = frame_incar.astype('uint8')
        # cv2.imwrite('face.png',faceimg)
        # self.facealign_result, self.headpose_result = self.headpose_module.run(frame_incar, face_bbox, logui_info)
        # self.facealign_result, self.headpose_result = self.headpose_module.run(frame_incar, self.face_bbox, logui_info)
        # lefteyelmks = [33,34,35,36,37,43,64,65,66,67,52,53,72,54,55,56,73,57,104,78]
        # righteyelmks = [38,39,40,41,42,68,69,70,71,58,59,75,60,61,62,76,63,105,43,79]
        # # pdb.set_trace()
        # facealign_result = np.array(self.facealign_result).reshape(-1,2)
        # leftrect = self.outterRect([facealign_result[i] for i in lefteyelmks],'left', size=(h,w))
        # rightrect = self.outterRect([facealign_result[i] for i in righteyelmks],'right', size=(h,w))
        # lefteyeimg = frame_incar[leftrect[1]:leftrect[3],leftrect[0]:leftrect[2],0]
        # righteyeimg = frame_incar[rightrect[1]:rightrect[3],rightrect[0]:rightrect[2],0]
        # smp = {}
        # smp['landmarks'] = [int(round(i)) for i in self.facealign_result]
        # smp['leftrect'] = leftrect
        # smp['rightrect'] = rightrect
        # smp['lefteyeimg'] = lefteyeimg.tolist()
        # smp['righteyeimg'] = righteyeimg.tolist()
        # smp['img_h'] = h
        # smp['img_w'] = w
        # gaze_input_info[formatted_time] = smp
        # cv2.imwrite('lefteyeimg.png',lefteyeimg)
        # cv2.imwrite('righteyeimg.png',righteyeimg)
        # # json.dump(gaze_input_info, open('gaze_input_info_eye_seq.json','w'))
        # frame_incar = np.ones((smp['img_h'],smp['img_w'],3))*np.mean(lefteyeimg.reshape(-1).tolist()+righteyeimg.reshape(-1).tolist())
        # frame_incar[leftrect[1]:leftrect[3],leftrect[0]:leftrect[2],0] = lefteyeimg
        # frame_incar[rightrect[1]:rightrect[3],rightrect[0]:rightrect[2],1] = righteyeimg
        # frame_incar[...,1] = frame_incar[...,0]
        # frame_incar[...,2] = frame_incar[...,0]
        # frame_incar = frame_incar.astype('uint8')
        # pdb.set_trace()
        # logui_info['frame_incar'] = frame_incar
        # self.left_eye3d, self.right_eye3d, self.hR = self.head3d_module.run(frame_incar, self.facealign_result, logui_info)
        # head3d_result = {
        #     "left_eye_position":self.left_eye3d,
        #     "right_eye_position": self.right_eye3d,
        #     "hR": cv2.Rodrigues(self.hR)[0]
        # }
        # eye_result = {'result': ('eye open', 'eye open')}

        # preprocess 
        # preprocessed_data = self.gaze_module.preprocess(frame_incar, head3d_result, self.facealign_result)
        # pdb.set_trace()
        # gaze_input_info = {}
        # gaze_input_info['left_input_data'] = preprocessed_data['left_input_data'].tolist()
        # gaze_input_info['right_input_data'] = preprocessed_data['right_input_data'].tolist()
        # gaze_input_info['le_R'] = preprocessed_data['le_R'].tolist()
        # gaze_input_info['re_R'] = preprocessed_data['re_R'].tolist()
        # gaze_input_info.update({
        #     "left_eye_position":self.left_eye3d.tolist(),
        #     "right_eye_position": self.right_eye3d.tolist(),
        #     "hR": cv2.Rodrigues(self.hR)[0].tolist()
        # })
        # gaze_input_info.update({'eye_result': ('eye open', 'eye open')})
        # gaze_input_info.update({'facealign_result':self.facealign_result})
        # json_str = CustomJSONEncoder(indent=None).encode(gaze_input_info)
        # json.dump(gaze_input_info, open('gaze_input_info.json','w'),indent=4)
        # pdb.set_trace()
        # gaze_input_info = json.load(open('gaze_input_info.json','r'))
        # ## load support info
        # preprocessed_data = {
        #     'left_input_data':np.array(gaze_input_info['left_input_data']).astype('float32'),
        #     'right_input_data':np.array(gaze_input_info['right_input_data']).astype('float32'),
        #     'le_R':np.array(gaze_input_info['le_R']),
        #     're_R':np.array(gaze_input_info['re_R']),
        # }
        # head3d_result = {
        #     "left_eye_position":np.array(gaze_input_info["left_eye_position"]),
        #     "right_eye_position": np.array(gaze_input_info["right_eye_position"]),
        #     "hR": np.array(gaze_input_info["hR"])
        # }
        # facealign_result = gaze_input_info['facealign_result']
        # eye_result = {'result':gaze_input_info['eye_result']}
        ## gaze estimate
        # self.gaze_result = self.gaze_module.run_croped(preprocessed_data, head3d_result, facealign_result, eye_result, logui_info)
        if gaze_input["gaze_valid"] == 0:
            self.logui_info = logui_info
            return


        
        print('DMS:',time.time()-cur_time); cur_time = time.time()
        ## for debug
        # minerror = 180
        # minidx = -1
        # def cosloss(gt,pred):
        #     res = np.sum(gt*pred)/(np.linalg.norm(gt)*np.linalg.norm(pred))
        #     angle = np.arccos(min(res,0.999999999999))/np.pi*180
        #     return angle
        # for tarnum in range(1,6):
        #     gt = np.array(logui_info['anchor'][tarnum-1]) - self.gaze_result['gaze_origin']
        #     gt = gt / np.linalg.norm(gt)
        #     error = cosloss(gt,self.gaze_result['mix_gaze'])
        #     if error < minerror:
        #         minidx = tarnum
        #         minerror = error
        #         meetgt = gt
        # self.gaze_result['mix_gaze'] = meetgt
        # pdb.set_trace()
        # c3d = np.array(self.car_bboxes['corners_3d'])
        # c3d[...,2] = c3d[...,2] - 4
        # self.car_bboxes['corners_3d'] = c3d.tolist()
        # self.gaze_result = pickle.load(open('gaze_result.pickle','rb'))
        # def vec2py(gaze_vec):
        #     length_gaze = np.linalg.norm(gaze_vec)
        #     gaze_vec = gaze_vec/length_gaze
        #     gaze_pitch = np.arcsin(-gaze_vec[1])
        #     gaze_yaw = np.arctan2(-gaze_vec[0], -gaze_vec[2])
        #     return gaze_pitch, gaze_yaw

        # def py2vec(gaze_pitch, gaze_yaw):
        #     gaze_transfer = np.zeros(3)
        #     gaze_transfer[0] = -np.cos(gaze_pitch) * np.sin(gaze_yaw)
        #     gaze_transfer[1] = -np.sin(gaze_pitch)
        #     gaze_transfer[2] = -np.cos(gaze_pitch) * np.cos(gaze_yaw)
        #     return gaze_transfer 
        # p, y = vec2py(self.gaze_result['mix_gaze'] )
        # pitch, yaw  = p/np.pi*180, y/np.pi*180-30
        # print(pitch,yaw)
        # vec = py2vec(pitch/180.*np.pi, yaw/180.*np.pi)
        # self.gaze_result['mix_gaze'] = vec

        self.car_bbox = self.gazecar_module.run(gaze_input, self.car_bboxes, logui_info)
        print('gaze_bbox:',time.time()-cur_time); cur_time = time.time()

        self.logui_info = logui_info
        # return logui_info
        # logui_img = Draw_logui_info(frame.copy(), logui_info)

    def run_eyeseq(self, frame_incar_infosf, frame_outcar, cur_time, logui_info={}, gaze_input_info={}):
        logui_info['frame_outcar'] = frame_outcar
        cur_time = time.time()
        self.frame_id += 1
        # pdb.set_trace()
        self.car_bboxes = self.car3Ddet_module.run(frame_outcar, logui_info, self.frame_id)
        logui_info['car_bboxes_res'] = self.car_bboxes
        print('car_bboxes:',time.time()-cur_time); cur_time = time.time()
        if len(self.car_bboxes.get('corners_3d',[])) == 0:
            logui_info['car_bboxes'] = False
        else:
            logui_info['Boxes'] = np.array(self.car_bboxes['corners_3d'])*1000
            logui_info['car_bboxes'] = True

        # self.face_bbox = self.wholebody_module.run(frame_incar, logui_info)
        if not logui_info['car_bboxes']:
            self.logui_info = logui_info
            return 
        car_logininfo = logui_info
        vote = []
        votedic = {}
        logui_info_list = []
        gaze_result_list = []
        car_bbox_list = []
        idx2time = {}
        # pdb.set_trace()
        # sortlist = np.argsort([float(i) for i in frame_incar_infos.keys()])
        # timelist = [list(frame_incar_infos.keys())[i] for i in sortlist]
        # frame_incar_infos = self.read_bin(frame_incar_infosf)
        # for idx, timeidx in enumerate(timelist):
        
        if frame_incar_infosf.get('frames',None) is None:
            logui_info['face_bbox'] = False
            frame_incar = np.ones((224,224,3)).astype('uint8')
            car_logininfo['frame_incar'] = frame_incar
            self.logui_info = car_logininfo
            return

        frame_incar_infos = frame_incar_infosf['frames']
        for idx in range(len(frame_incar_infos)):
            logui_info = {}
            # idx2time[idx] = timeidx
            incar_info = frame_incar_infos[idx]
            # pdb.set_trace()
            if incar_info.get('landmarks',None) is None or \
                incar_info.get('leftrect',None) is None or \
                incar_info.get('rightrect',None) is None or \
                incar_info.get('lefteyeimg',None) is None or \
                incar_info.get('righteyeimg',None) is None or \
                np.array(incar_info['lefteyeimg']).shape[0] != incar_info['leftrect'][3]-incar_info['leftrect'][1] or \
                np.array(incar_info['lefteyeimg']).shape[1] != incar_info['leftrect'][2]-incar_info['leftrect'][0] or \
                np.array(incar_info['righteyeimg']).shape[0] != incar_info['rightrect'][3]-incar_info['rightrect'][1] or \
                np.array(incar_info['righteyeimg']).shape[1] != incar_info['rightrect'][2]-incar_info['rightrect'][0] or \
                len(incar_info.get('leftrect',None)) == 0  or len(incar_info.get('rightrect',None)) == 0:
                logui_info['face_bbox'] = False
            else:
                logui_info['face_bbox'] = True
            
            if not logui_info['face_bbox']:
                logui_info_list.append(logui_info)
                vote.append(-1)
                gaze_result_list.append(-1)
                car_bbox_list.append(-1)
                votedic[-1] = votedic.get(-1,0)+1
                # self.logui_info = logui_info
            else:
                facealign_result = np.array(incar_info['landmarks']).astype('float32')
                # pdb.set_trace()
                leftrect = incar_info['leftrect'] 
                rightrect = incar_info['rightrect'] 
                lefteyeimg = np.array(incar_info['lefteyeimg'])
                righteyeimg = np.array(incar_info['righteyeimg'])
                frame_incar = np.ones((incar_info['img_h'],incar_info['img_w'],3))*np.mean(lefteyeimg.reshape(-1).tolist()+righteyeimg.reshape(-1).tolist())
                frame_incar[leftrect[1]:leftrect[3],leftrect[0]:leftrect[2],0] = lefteyeimg
                frame_incar[rightrect[1]:rightrect[3],rightrect[0]:rightrect[2],0] = righteyeimg
                frame_incar[...,1] = frame_incar[...,0]
                frame_incar[...,2] = frame_incar[...,0]
                frame_incar = frame_incar.astype('uint8')
                cur_time = time.time()
                # self.facealign_result, self.headpose_result = self.headpose_module.run(frame_incar, face_bbox, logui_info)
                # self.facealign_result, self.headpose_result = self.headpose_module.run(frame_incar, self.face_bbox, logui_info)
                # self.left_eye3d, self.right_eye3d, self.hR = self.head3d_module.run(frame_incar, self.facealign_result, logui_info)
                self.left_eye3d, self.right_eye3d, self.hR = self.head3d_module.run(frame_incar, facealign_result, logui_info)
                head3d_result = {
                    "left_eye_position":self.left_eye3d,
                    "right_eye_position": self.right_eye3d,
                    "hR": cv2.Rodrigues(self.hR)[0]
                }
                eye_result = {'result': ('eye open', 'eye open')}
                self.gaze_result = self.gaze_module.run(frame_incar, head3d_result, facealign_result, eye_result, logui_info)
                # pdb.set_trace()
                print(self.gaze_result)
                print('DMS:',time.time()-cur_time); cur_time = time.time()
                self.car_bbox = self.gazecar_module.run(self.gaze_result, self.car_bboxes, logui_info)
                print('gaze_bbox:',time.time()-cur_time); cur_time = time.time()
                gaze_result_list.append(self.gaze_result)
                car_bbox_list.append(self.car_bbox)
                logui_info_list.append(logui_info)
                vote.append(self.car_bbox.get('box_idx',-1))
                votedic[self.car_bbox.get('box_idx',-1)] = votedic.get(self.car_bbox.get('box_idx',-1),0)+1
                # pdb.set_trace()
        # np.max(votedic.values())
        maxvote = max(votedic.values())
        votedidx = [i for i,j in votedic.items() if j==maxvote]
        chosenidx = min([vote.index(i) for i in votedidx])
        self.gaze_result = gaze_result_list[chosenidx]
        self.car_bbox = car_bbox_list[chosenidx]
        logui_info = logui_info_list[chosenidx]
        # pdb.set_trace()
        car_logininfo.update(logui_info)
        incar_info = frame_incar_infos[chosenidx]#[idx2time[chosenidx]]
        if maxvote == -1:
            frame_incar = np.ones((incar_info.get('img_h',224),incar_info.get('img_w',224),3)).astype('uint8')
        else:
            leftrect = incar_info['leftrect'] 
            rightrect = incar_info['rightrect'] 
            lefteyeimg = np.array(incar_info['lefteyeimg'])
            righteyeimg = np.array(incar_info['righteyeimg'])
            frame_incar = np.ones((incar_info.get('img_h',1080),incar_info.get('img_w',1920),3))*np.mean(lefteyeimg.reshape(-1).tolist()+righteyeimg.reshape(-1).tolist())
            frame_incar[leftrect[1]:leftrect[3],leftrect[0]:leftrect[2],0] = lefteyeimg
            frame_incar[rightrect[1]:rightrect[3],rightrect[0]:rightrect[2],0] = righteyeimg
            frame_incar[...,1] = frame_incar[...,0]
            frame_incar[...,2] = frame_incar[...,0]
            frame_incar = frame_incar.astype('uint8')
        car_logininfo['frame_incar'] = frame_incar
        self.logui_info = car_logininfo

    def outterRect(self, points, side, size):
        points = np.array(points)
        min_x = np.min(points[:, 0])
        min_y = np.min(points[:, 1])
        max_x = np.max(points[:, 0])
        max_y = np.max(points[:, 1])
        if side == 'left':
            min_x = np.clip(min_x - 1/4*(abs(max_x-min_x)+1),0,size[1])
            max_y = np.clip(max_y + 1/2*(abs(max_y-min_y)+1),0,size[0])
        else:
            max_x = np.clip(max_x + 1/4*(abs(max_x-min_x)+1),0,size[1])
            max_y = np.clip(max_y + 1/2*(abs(max_y-min_y)+1),0,size[0])

        # top_left = (min_x, min_y)
        # bottom_right = (max_x, max_y)
        return [int(min_x), int(min_y), int(max_x), int(max_y)]

    def get_car_bbox(self):
        return self.car_bbox

    def get_raw_gaze(self):
        return self.gaze_result
    
    def get_logui_info(self):
        return self.logui_info
    
    def get_output_info(self):
        camerainfo = json.load(open(self.camera_path,'r'))

        labels_3d = self.car_bboxes['labels_3d']
        bboxes_3d = self.car_bboxes['bboxes_3d']
        car_det = []
        car_det_corner = []
        person_det = []

        for i in range(len(labels_3d)):
            if labels_3d[i] in [0, 1, 2, 3, 4]:
                car_id = len(car_det) + 1
                car_det.append(
                    {
                        "carID": f"car{car_id}",
                        "center(m)": bboxes_3d[i][:3],
                        "width(m)":  bboxes_3d[i][5],
                        "height(m)": bboxes_3d[i][4],
                        "length(m)": bboxes_3d[i][3]
                    }
                )
                car_det_corner.append(self.car_bboxes['corners_3d'][i])
            
            if labels_3d[i] in [5, 6, 7]:
                person_id = len(person_det) + 1
                person_det.append(
                    {
                        "personID": f"person{person_id}",
                        "center(m)": bboxes_3d[i][:3],
                        "width(m)":  bboxes_3d[i][5],
                        "height(m)": bboxes_3d[i][4],
                        "length(m)": bboxes_3d[i][3]
                    }
                )
        # pdb.set_trace()
        if self.logui_info['car_bboxes'] and self.logui_info['boxRes']['valid']:
            outK = np.array(camerainfo['OutcarCamera']['CameraIntrinsics'])
            outdist = np.array(camerainfo['OutcarCamera'].get('Distortion',[0.0,0,0,0,0]))
            select_car_id = self.car_bbox['box_idx'] + 1
            select_corners_3d = self.car_bbox['box_corner']
            undist_proj_2d = cv2.projectPoints(select_corners_3d, np.zeros(3), np.zeros(3), outK, np.zeros(8))[0]
            x, y, w, h = cv2.boundingRect(undist_proj_2d.astype(np.float32))
            undist_rect_2d = [x, y, x + w, y + h]
            # pdb.set_trace()
            if camerainfo['OutcarCamera']['Camera_type'] in ['fisheye','OMS']:
                ori_outK = np.array(camerainfo['OutcarCamera']['CameraIntrinsics_ori'])
                proj_2d = cv2.fisheye.projectPoints(select_corners_3d[:, np.newaxis, :], np.zeros(3), np.zeros(3), ori_outK, outdist)[0]
            else:
                proj_2d = cv2.projectPoints(select_corners_3d, np.zeros(3), np.zeros(3), outK, outdist)[0]
            x, y, w, h = cv2.boundingRect(proj_2d.astype(np.float32))
            rect_2d = [x, y, x + w, y + h]
            height, width = self.logui_info['frame_outcar'].shape[:2]
            scaled_rect_2d = [int(rect_2d[0]/width*1000.),int(rect_2d[1]/height*1000.),int(rect_2d[2]/width*1000.),int(rect_2d[3]/height*1000.)]
            gaze_bboxes = []
            for corners3d in car_det_corner:
                corners3d = np.array(corners3d) * 1000
                # pdb.set_trace()
                if camerainfo['OutcarCamera']['Camera_type'] in ['fisheye','OMS']:
                    ori_outK = np.array(camerainfo['OutcarCamera']['CameraIntrinsics_ori'])
                    proj_2ds = cv2.fisheye.projectPoints(corners3d[:, np.newaxis, :], np.zeros(3), np.zeros(3), ori_outK, outdist)[0]
                else:
                    proj_2ds = cv2.projectPoints(corners3d, np.zeros(3), np.zeros(3), outK, outdist)[0]
                x, y, w, h = cv2.boundingRect(proj_2ds.astype(np.float32))
                rect_2ds = [x, y, x + w, y + h]
                gaze_bboxes.append(rect_2ds)
            
            gaze_select = [
                {
                    "itemID": f"car{select_car_id}",
                    "Undist2DRect": undist_rect_2d,
                    "2DRect": scaled_rect_2d,
                    "2DRect_ori": rect_2d,
                    "2DRects": gaze_bboxes
                }
            ]
            # pdb.set_trace()
        else:
            gaze_select = []
        gaze_car = []
        gazeorigin_car = []
        if self.logui_info['car_bboxes']:
            gaze_car = self.logui_info['RayRes']['gaze_vec'].tolist()
            gazeorigin_car = self.logui_info['RayRes']['gaze_origin'].tolist()
            
        output_result = {
            "Gaze": gaze_car,
            "Head3d(mm)": gazeorigin_car,
            "CarDet": car_det,
            "PedestDet": person_det,
            "GazeSelected": gaze_select
        }
        return output_result

class ModelsWarpper_seqdms(ModelsWarpper):
    def run(self, frame_incar_infos, frame_outcar, cur_time, logui_info={}, gaze_input_info={}):
        logui_info['frame_outcar'] = frame_outcar
        cur_time = time.time()
        self.frame_id += 1
        # pdb.set_trace()
        self.car_bboxes = self.car3Ddet_module.run(frame_outcar, logui_info, self.frame_id)
        logui_info['car_bboxes_res'] = self.car_bboxes
        print('car_bboxes:',time.time()-cur_time); cur_time = time.time()
        if len(self.car_bboxes.get('corners_3d',[])) == 0:
            logui_info['car_bboxes'] = False
        else:
            logui_info['Boxes'] = np.array(self.car_bboxes['corners_3d'])*1000
            logui_info['car_bboxes'] = True

        # self.face_bbox = self.wholebody_module.run(frame_incar, logui_info)
        if not logui_info['car_bboxes']:
            self.logui_info = logui_info
            return 
        car_logininfo = logui_info
        vote = []
        votedic = {}
        logui_info_list = []
        gaze_result_list = []
        car_bbox_list = []
        idx2time = {}
        # pdb.set_trace()
        sortlist = np.argsort([float(i) for i in frame_incar_infos.keys()])
        timelist = [list(frame_incar_infos.keys())[i] for i in sortlist]
        for idx, timeidx in enumerate(timelist):
            logui_info = {}
            idx2time[idx] = timeidx
            incar_info = frame_incar_infos[timeidx]

            face_bbox = incar_info['face_bbox']
            faceimg = np.array(incar_info['faceimg'])
            frame_incar = np.ones((incar_info['img_h'],incar_info['img_w'],3))*120
            frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],0] = faceimg
            frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],1] = faceimg
            frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],2] = faceimg
            frame_incar = frame_incar.astype('uint8')
            if len(face_bbox) == 0 :
                logui_info['face_bbox'] = False
            else:
                logui_info['face_bbox'] = True

            if not logui_info['face_bbox']:
                logui_info_list.append(logui_info)
                vote.append(-1)
                gaze_result_list.append(-1)
                car_bbox_list.append(-1)
                votedic[-1] = votedic.get(-1,0)+1
                # self.logui_info = logui_info
            else:
                cur_time = time.time()
                self.facealign_result, self.headpose_result = self.headpose_module.run(frame_incar, face_bbox, logui_info)
                # self.facealign_result, self.headpose_result = self.headpose_module.run(frame_incar, self.face_bbox, logui_info)
                self.left_eye3d, self.right_eye3d, self.hR = self.head3d_module.run(frame_incar, self.facealign_result, logui_info)
                head3d_result = {
                    "left_eye_position":self.left_eye3d,
                    "right_eye_position": self.right_eye3d,
                    "hR": cv2.Rodrigues(self.hR)[0]
                }
                eye_result = {'result': ('eye open', 'eye open')}
                self.gaze_result = self.gaze_module.run(frame_incar, head3d_result, self.facealign_result, eye_result, logui_info)
                # print(self.gaze_result)
                print('DMS:',time.time()-cur_time); cur_time = time.time()
                self.car_bbox = self.gazecar_module.run(self.gaze_result, self.car_bboxes, logui_info)
                print('gaze_bbox:',time.time()-cur_time); cur_time = time.time()
                gaze_result_list.append(self.gaze_result)
                car_bbox_list.append(self.car_bbox)
                logui_info_list.append(logui_info)
                vote.append(self.car_bbox['box_idx'])
                votedic[self.car_bbox['box_idx']] = votedic.get(self.car_bbox['box_idx'],0)+1
                # pdb.set_trace()
        # np.max(votedic.values())
        maxvote = max(votedic.values())
        votedidx = [i for i,j in votedic.items() if j==maxvote]
        chosenidx = min([vote.index(i) for i in votedidx])
        self.gaze_result = gaze_result_list[chosenidx]
        self.car_bbox = car_bbox_list[chosenidx]
        logui_info = logui_info_list[chosenidx]
        # pdb.set_trace()
        car_logininfo.update(logui_info)
        incar_info = frame_incar_infos[idx2time[chosenidx]]
        face_bbox = incar_info['face_bbox']
        faceimg = np.array(incar_info['faceimg'])
        frame_incar = np.ones((incar_info['img_h'],incar_info['img_w'],3))*120
        frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],0] = faceimg
        frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],1] = faceimg
        frame_incar[face_bbox[1]:face_bbox[3],face_bbox[0]:face_bbox[2],2] = faceimg
        frame_incar = frame_incar.astype('uint8')
        car_logininfo['frame_incar'] = frame_incar
        self.logui_info = car_logininfo
        
class ModelsWarpper_eyeseqdms(ModelsWarpper):
    def run(self, frame_incar_infosf, frame_outcar, cur_time, logui_info={}, gaze_input_info={}):
        logui_info['frame_outcar'] = frame_outcar
        cur_time = time.time()
        self.frame_id += 1
        # pdb.set_trace()
        self.car_bboxes = self.car3Ddet_module.run(frame_outcar, logui_info, self.frame_id)
        logui_info['car_bboxes_res'] = self.car_bboxes
        print('car_bboxes:',time.time()-cur_time); cur_time = time.time()
        if len(self.car_bboxes.get('corners_3d',[])) == 0:
            logui_info['car_bboxes'] = False
        else:
            logui_info['Boxes'] = np.array(self.car_bboxes['corners_3d'])*1000
            logui_info['car_bboxes'] = True

        # self.face_bbox = self.wholebody_module.run(frame_incar, logui_info)
        if not logui_info['car_bboxes']:
            self.logui_info = logui_info
            return 
        car_logininfo = logui_info
        vote = []
        votedic = {}
        logui_info_list = []
        gaze_result_list = []
        car_bbox_list = []
        idx2time = {}
        # pdb.set_trace()
        # sortlist = np.argsort([float(i) for i in frame_incar_infos.keys()])
        # timelist = [list(frame_incar_infos.keys())[i] for i in sortlist]
        # frame_incar_infos = self.read_bin(frame_incar_infosf)
        # for idx, timeidx in enumerate(timelist):
        
        if frame_incar_infosf.get('frames',None) is None:
            logui_info['face_bbox'] = False
            frame_incar = np.ones((224,224,3)).astype('uint8')
            car_logininfo['frame_incar'] = frame_incar
            self.logui_info = car_logininfo
            return

        frame_incar_infos = frame_incar_infosf['frames']
        for idx in range(len(frame_incar_infos)):
            logui_info = {}
            # idx2time[idx] = timeidx
            incar_info = frame_incar_infos[idx]
            # pdb.set_trace()
            if incar_info.get('landmarks',None) is None or \
                incar_info.get('leftrect',None) is None or \
                incar_info.get('rightrect',None) is None or \
                incar_info.get('lefteyeimg',None) is None or \
                incar_info.get('righteyeimg',None) is None or \
                np.array(incar_info['lefteyeimg']).shape[0] != incar_info['leftrect'][3]-incar_info['leftrect'][1] or \
                np.array(incar_info['lefteyeimg']).shape[1] != incar_info['leftrect'][2]-incar_info['leftrect'][0] or \
                np.array(incar_info['righteyeimg']).shape[0] != incar_info['rightrect'][3]-incar_info['rightrect'][1] or \
                np.array(incar_info['righteyeimg']).shape[1] != incar_info['rightrect'][2]-incar_info['rightrect'][0] or \
                len(incar_info.get('leftrect',None)) == 0  or len(incar_info.get('rightrect',None)) == 0:
                logui_info['face_bbox'] = False
            else:
                logui_info['face_bbox'] = True
            
            if not logui_info['face_bbox']:
                logui_info_list.append(logui_info)
                vote.append(-1)
                gaze_result_list.append(-1)
                car_bbox_list.append(-1)
                votedic[-1] = votedic.get(-1,0)+1
                # self.logui_info = logui_info
            else:
                facealign_result = np.array(incar_info['landmarks']).astype('float32')
                # pdb.set_trace()
                leftrect = incar_info['leftrect'] 
                rightrect = incar_info['rightrect'] 
                lefteyeimg = np.array(incar_info['lefteyeimg'])
                righteyeimg = np.array(incar_info['righteyeimg'])
                frame_incar = np.ones((incar_info['img_h'],incar_info['img_w'],3))*np.mean(lefteyeimg.reshape(-1).tolist()+righteyeimg.reshape(-1).tolist())
                frame_incar[leftrect[1]:leftrect[3],leftrect[0]:leftrect[2],0] = lefteyeimg
                frame_incar[rightrect[1]:rightrect[3],rightrect[0]:rightrect[2],0] = righteyeimg
                frame_incar[...,1] = frame_incar[...,0]
                frame_incar[...,2] = frame_incar[...,0]
                frame_incar = frame_incar.astype('uint8')
                cur_time = time.time()
                # self.facealign_result, self.headpose_result = self.headpose_module.run(frame_incar, face_bbox, logui_info)
                # self.facealign_result, self.headpose_result = self.headpose_module.run(frame_incar, self.face_bbox, logui_info)
                # self.left_eye3d, self.right_eye3d, self.hR = self.head3d_module.run(frame_incar, self.facealign_result, logui_info)
                self.left_eye3d, self.right_eye3d, self.hR = self.head3d_module.run(frame_incar, facealign_result, logui_info)
                head3d_result = {
                    "left_eye_position":self.left_eye3d,
                    "right_eye_position": self.right_eye3d,
                    "hR": cv2.Rodrigues(self.hR)[0]
                }
                eye_result = {'result': ('eye open', 'eye open')}
                self.gaze_result = self.gaze_module.run(frame_incar, head3d_result, facealign_result, eye_result, logui_info)
                # pdb.set_trace()
                print(self.gaze_result)
                print('DMS:',time.time()-cur_time); cur_time = time.time()
                self.car_bbox = self.gazecar_module.run(self.gaze_result, self.car_bboxes, logui_info)
                print('gaze_bbox:',time.time()-cur_time); cur_time = time.time()
                gaze_result_list.append(self.gaze_result)
                car_bbox_list.append(self.car_bbox)
                logui_info_list.append(logui_info)
                vote.append(self.car_bbox.get('box_idx',-1))
                votedic[self.car_bbox.get('box_idx',-1)] = votedic.get(self.car_bbox.get('box_idx',-1),0)+1
                # pdb.set_trace()
        # np.max(votedic.values())
        maxvote = max(votedic.values())
        votedidx = [i for i,j in votedic.items() if j==maxvote]
        chosenidx = min([vote.index(i) for i in votedidx])
        self.gaze_result = gaze_result_list[chosenidx]
        self.car_bbox = car_bbox_list[chosenidx]
        logui_info = logui_info_list[chosenidx]
        # pdb.set_trace()
        car_logininfo.update(logui_info)
        incar_info = frame_incar_infos[chosenidx]#[idx2time[chosenidx]]
        if maxvote == -1:
            frame_incar = np.ones((incar_info.get('img_h',224),incar_info.get('img_w',224),3)).astype('uint8')
        else:
            leftrect = incar_info['leftrect'] 
            rightrect = incar_info['rightrect'] 
            lefteyeimg = np.array(incar_info['lefteyeimg'])
            righteyeimg = np.array(incar_info['righteyeimg'])
            frame_incar = np.ones((incar_info.get('img_h',1080),incar_info.get('img_w',1920),3))*np.mean(lefteyeimg.reshape(-1).tolist()+righteyeimg.reshape(-1).tolist())
            frame_incar[leftrect[1]:leftrect[3],leftrect[0]:leftrect[2],0] = lefteyeimg
            frame_incar[rightrect[1]:rightrect[3],rightrect[0]:rightrect[2],0] = righteyeimg
            frame_incar[...,1] = frame_incar[...,0]
            frame_incar[...,2] = frame_incar[...,0]
            frame_incar = frame_incar.astype('uint8')
        car_logininfo['frame_incar'] = frame_incar
        self.logui_info = car_logininfo
    
    def read_bin(self, f):
        imghwl_bytes = f.read(2*3)
        imghwl = np.frombuffer(imghwl_bytes, dtype=np.uint16)
        # print(imghwl)
        smp = {}
        smp['img_h'] = imghwl[0]
        smp['img_w'] = imghwl[1]
        smp['seq_len'] = imghwl[2]
    #     # 读取 uint8 数组
        pixel_num = []
        for i in range(imghwl[-1]):
            smp[i] = {}
            lmks = np.frombuffer(f.read(2*106*2), dtype=np.uint16)
            datarect = np.frombuffer(f.read(2*8), dtype=np.uint16)
            leftrect, rightrect = datarect[:4], datarect[-4:]
            lrectw = (leftrect[2] - leftrect[0]).astype('int')
            lrecth = (leftrect[3] - leftrect[1]).astype('int')
            # print(leftrect,lrecth,lrectw)
            # pixel_num.append(lrecth*lrectw)
            rrectw = (rightrect[2] - rightrect[0]).astype('int')
            rrecth = (rightrect[3] - rightrect[1]).astype('int')
            # print(rightrect,rrecth,rrectw)
            # pixel_num.append(rrecth*rrectw)
            smp[i]['landmarks'] = lmks
            if lrectw <= 0 or lrecth<=0 or rrectw <= 0 or rrecth<=0:
                smp[i]['leftrect'] = []
                smp[i]['rightrect'] = []
                return smp
            else:
                smp[i]['leftrect'] = leftrect
                smp[i]['rightrect'] = rightrect
            lefteye = np.frombuffer(f.read(lrectw*lrecth), dtype=np.uint8)
            righteye = np.frombuffer(f.read(rrectw*rrecth), dtype=np.uint8)
            # cv2.imwrite('left_{}.png'.format(i),lefteye.reshape(lrecth,lrectw))
            # cv2.imwrite('right_{}.png'.format(i),righteye.reshape(rrecth,rrectw))
            
            smp[i]['lefteye'] = lefteye.reshape(lrecth,lrectw)
            smp[i]['righteye'] = righteye.reshape(rrecth,rrectw)
        return smp

