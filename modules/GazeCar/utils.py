import numpy as np 
import pdb 
from scipy.spatial.transform import Rotation as R
import cv2 

def vec2py(gaze_vec):
    length_gaze = np.linalg.norm(gaze_vec)
    gaze_vec = gaze_vec/length_gaze
    gaze_pitch = np.arcsin(-gaze_vec[1])
    gaze_yaw = np.arctan2(gaze_vec[0], gaze_vec[2])
    return gaze_pitch, gaze_yaw

def py2vec(gaze_pitch, gaze_yaw):
    gaze_transfer = np.zeros(3)
    gaze_transfer[0] = np.cos(gaze_pitch) * np.sin(gaze_yaw)
    gaze_transfer[1] = -np.sin(gaze_pitch)
    gaze_transfer[2] = np.cos(gaze_pitch) * np.cos(gaze_yaw)
    return gaze_transfer 

def pred2corners(gt_bboxes_3d_):
    dims = gt_bboxes_3d_[:,3:6]
    corners_norm_np = np.stack(np.unravel_index(np.arange(8), [2] * 3), axis=1)

    corners_norm_np = corners_norm_np[[0, 1, 3, 2, 4, 5, 7, 6]]
    # use relative origin (0.5, 1, 0.5)
    corners_norm_np = corners_norm_np - np.array([0.5, 1, 0.5])
    corners = dims.reshape(-1, 1, 3) * corners_norm_np.reshape([1, 8, 3])

    # corners = rotation_3d_in_axis(
    #     corners, self.tensor[:, 6], axis=self.YAW_AXIS)
    angles = gt_bboxes_3d_[:,6]
    rot_sin = np.sin(angles)
    rot_cos = np.cos(angles)
    ones = np.ones_like(rot_cos)
    zeros = np.zeros_like(rot_cos)
    # rot_mat_T = np.stack([np.stack([rot_cos, rot_sin, zeros]),np.stack([-rot_sin, rot_cos, zeros]),np.stack([zeros, zeros, ones])])
    rot_mat_T = np.stack([np.stack([rot_cos, zeros, -rot_sin]),np.stack([zeros, ones, zeros]),np.stack([rot_sin, zeros, rot_cos])])
    corners = np.einsum('aij,jka->aik', corners, rot_mat_T)
    corners += gt_bboxes_3d_[:,:3].reshape(-1, 1, 3)
    return corners

def pt3dto2d(corners, K):
    points_4 = np.concatenate([corners, np.ones((*corners.shape[:-1],1))], axis=-1)
    proj_mat = np.eye(4)
    proj_mat[:3,:3] = K
    point_2d = points_4 @ proj_mat.T
    point_2d_res = point_2d[..., :2] / point_2d[..., 2:3]
    point_2d_res = np.round(point_2d_res - 1)
    imgfov_pts_2d = point_2d_res.reshape(-1,8,2)
    return imgfov_pts_2d

def fit_plane(points):
    """
    给定 3D 空间中的点，拟合离这些点距离最近的平面
    """
    # 提取 x, y, z 坐标
    XZ = points[:, [0,2]]  # 提取 x 和 z 坐标
    Y = points[:, 1]   # 提取 y 坐标

    # 在 X 中添加一列常数项 1
    XZ = np.hstack((XZ, np.ones((XZ.shape[0], 1))))

    # 最小二乘法求解平面参数
    # Z = X @ [a, b, c]
    coef = np.linalg.lstsq(XZ, Y, rcond=None)[0]

    # 平面方程系数 a, b, c
    a, b, c = coef
    return a, b, c

def align_plane_to_xz(plane_normal):
    """
    计算旋转矩阵，使平面法向量对齐到XZ平面 (即使其法向量变为 (0,1,0))

    :param plane_normal: (A, B, C) 原平面的法向量
    :return: 旋转矩阵 (3x3)
    """
    A, B, C = plane_normal
    original_normal = np.array([A, B, C])
    target_normal = np.array([0, 1, 0])  # XZ 平面的法向量

    # 计算旋转轴 (叉乘)
    rotation_axis = np.cross(original_normal, target_normal)
    if np.linalg.norm(rotation_axis) < 1e-6:  # 如果已经平行，就不需要旋转
        return np.eye(3)

    # 计算旋转角度
    cos_theta = np.dot(original_normal, target_normal) / (np.linalg.norm(original_normal) * np.linalg.norm(target_normal))
    theta = np.arccos(np.clip(cos_theta, -1.0, 1.0))  # 限制值避免浮点误差

    # 生成旋转矩阵
    rotation_matrix = R.from_rotvec(theta * rotation_axis / np.linalg.norm(rotation_axis)).as_matrix()
    
    return rotation_matrix

def rotate_points(points, rotation_matrix):
    """
    旋转 3D 点集
    :param points: Nx3 的点集
    :param rotation_matrix: 3x3 旋转矩阵
    :return: 旋转后的点集
    """
    return np.dot(points, rotation_matrix.T)

def project_point_onto_plane(point, plane_normal, d):
    """
    计算空间中一个点在平面上的投影。

    :param point: (x0, y0, z0) 需要投影的点
    :param plane_normal: (A, B, C) 平面的法向量
    :param d: 平面方程 Ax + By + Cz + D = 0 中的 D
    :return: (x', y', z') 投影点坐标
    """
    # x0, y0, z0 = point
    A, B, C = plane_normal

    # 计算点到平面的垂直距离
    distance = (A * point[...,0:1] + B * point[...,1:2] + C * point[...,2:] + d) / (A**2 + B**2 + C**2)

    # 计算投影点坐标
    x_prime = point[...,0:1] - A * distance
    y_prime = point[...,1:2] - B * distance
    z_prime = point[...,2:] - C * distance

    return np.concatenate([x_prime, y_prime, z_prime],axis=-1)

class FindCrossBox:
    def __init__(self,origin,vector,boxes,config):
        self.config = config
        self.origin = origin 
        self.vector = vector 
        # self.boxes = boxes 
        self.corners3d = np.array(boxes)*1000
        self.boxlineidx = [
            [4,5],#前上 
            [7,6],#前下 
            [4,7],#前左 
            [5,6],#前右 
            [0,1],#后上 
            [3,2],#后下 
            [0,3],#后左 
            [1,2],#后右 
            [4,0],
            [5,1],
            [6,2],
            [7,3],
        ]
        self.boxlineidx_xz = [
            [4,5],#前上 
            [7,6],#前下 
            [0,1],#后上 
            [3,2],#后下 
            [4,0],#左上
            [5,1],#右上
            [6,2],#右下
            [7,3],#左下
        ]
    
    def get_crossRes(self):
        return {'interactioin_in_surface':self.minintersections,
                'surface_corner':self.minintersections_face,
                'box_corner':self.minintersections_box,
                'box_idx':self.minbox_idx,
                'type':'Cross'}
    
    def get_nearestRes(self):
        return {'interactioin_on_ray_seg':self.minintersections,
                'line_corner':self.minintersections_line,
                'box_corner':self.minintersections_box,
                'box_idx':self.minbox_idx,
                'type':'Nearest'}
    
    def find_endBox(self):
        # pdb.set_trace()
        # self.corners3d = pred2corners(self.boxes)*1000
        crossflag, mindist = self.find_crossPoint()
        if not crossflag:
            mindist = self.find_nearestPointv2()
        return crossflag, mindist

    def find_nearestPoint(self):
        '''
        find the nearest point on car 3d to the gaze ray return minimum distance and point info
        '''
        mindist = np.inf
        self.minintersections = []
        self.minintersections_line = []
        self.minintersections_box = []
        for idx in range(self.corners3d.shape[0]):
            # if idx == 2:
            #     pdb.set_trace()
            raypt, segpt, dist, intersections_line = self.ray_to_face_distance(self.origin, self.vector, self.corners3d[idx])
            if dist < mindist:
                self.minintersections = [raypt, segpt]
                self.minintersections_line = [self.corners3d[idx][self.boxlineidx[intersections_line][0]], self.corners3d[idx][self.boxlineidx[intersections_line][1]]]
                self.minintersections_box = self.corners3d[idx]
                self.minbox_idx = idx
                mindist = dist
        return mindist

    def find_nearestPointv2(self):
        '''
        find the nearest 2d point on car to gaze ray in ground surface
        '''
        cross_mindist = np.inf
        cross_minintersections = []
        cross_minintersections_line = []
        cross_minintersections_box = []

        nocross_mindist = np.inf
        nocross_minintersections = []
        nocross_minintersections_line = []
        nocross_minintersections_box = []
        
        match_box_exist_flag = False
        self.minintersections = []
        self.minintersections_line = []
        self.minintersections_box = []
        bottom_corners = self.corners3d.reshape(-1,8,3)[:,[2,3,6,7]]
        param = fit_plane(bottom_corners.reshape(-1,3))
        ## fix plant
    #     def VanishY2Pitch(vy, K):
    #         fy, cy = K[1][1], K[1][2]
    #         pitch = -np.arctan2(vy - cy, fy)
    #         return pitch
    #     outK = np.array([[1.01448851e+03, 0.00000000e+00, 9.56419999e+02],
    #    [0.00000000e+00, 1.02232450e+03, 5.38781706e+02],
    #    [0.00000000e+00, 0.00000000e+00, 1.00000000e+00]])
    #     pitch = VanishY2Pitch(520,outK)
    #     h = 1.23 * 1000
    #     param = [0, -np.sin(pitch)/np.cos(pitch), h/np.cos(pitch)]
        plane_normal = np.array([param[0],-1,param[1]])  # 平面法向量 (A, B, C)
        projected_point = project_point_onto_plane(self.corners3d.reshape(-1,8,3), plane_normal, param[2])
        rotation_matrix = align_plane_to_xz(plane_normal)
        # pdb.set_trace()
        corners2d = rotate_points(projected_point, rotation_matrix)[...,[0,2]]
        endpoint = self.origin + self.vector * 100000
        gazepts = np.array([self.origin,endpoint])
        gazeprojected_point = project_point_onto_plane(gazepts, plane_normal, param[2])
        gaze2d = rotate_points(gazeprojected_point, rotation_matrix)[...,[0,2]]
        gazevec2d = gaze2d[1] - gaze2d[0]
        gazevec2d = gazevec2d / np.linalg.norm(gazevec2d)
        # pdb.set_trace()
        ## debug 
        debug = False
        if debug:
            allpts = np.concatenate([corners2d.reshape(-1,2) , gaze2d],axis=0)
            maxxy = np.max(allpts[:-1],axis=0)
            minxy = np.min(allpts[:-1],axis=0)
            maxx = np.max(np.abs([maxxy[0],minxy[0]]))
            maxy = abs(maxxy[1])+abs(minxy[1])
            x_rate = maxx / ((1080-40)/2)
            y_rate = maxy / (720-40)
            rate = max(x_rate,y_rate)
            height = abs(min(allpts[:-1,1])) / rate
            self.debug_img = np.ones((720,1080,3)).astype('uint8')*255
            # pdb.set_trace()
        for idx in range(corners2d.shape[0]):
            # if idx == 2:
            #     pdb.set_trace()
            if debug:
                box = corners2d[idx]
                box = box / rate 
                box[...,0] += 1080/2 
                box[...,1] += height

                lines = [
                    [box[self.boxlineidx_xz[0][0]],box[self.boxlineidx_xz[0][1]]],#前上 
                    [box[self.boxlineidx_xz[1][0]],box[self.boxlineidx_xz[1][1]]],#前下 
                    [box[self.boxlineidx_xz[2][0]],box[self.boxlineidx_xz[2][1]]],#前左 
                    [box[self.boxlineidx_xz[3][0]],box[self.boxlineidx_xz[3][1]]],#前右 
                    [box[self.boxlineidx_xz[4][0]],box[self.boxlineidx_xz[4][1]]],#后上 
                    [box[self.boxlineidx_xz[5][0]],box[self.boxlineidx_xz[5][1]]],#后下 
                    [box[self.boxlineidx_xz[6][0]],box[self.boxlineidx_xz[6][1]]],#后左 
                    [box[self.boxlineidx_xz[7][0]],box[self.boxlineidx_xz[7][1]]],#后右 
                ]
                for line in lines:
                    cv2.line(self.debug_img, line[0].astype('int'), 
                        line[1].astype('int'), (0,0,255), thickness=2)
                    # pdb.set_trace()
                # cv2.imwrite('debug.png', self.debug_img)
            segpt2d, segpt3d, dist, intersections_line, crossflag, findflag = self.ray_to_line_distance(gaze2d[0], gazevec2d, corners2d[idx], self.corners3d[idx])
            # pdb.set_trace()
            if findflag:
                match_box_exist_flag = True
                if crossflag:
                    if dist < cross_mindist:
                        # pdb.set_trace()
                        cross_minintersections = [segpt3d, segpt3d]
                        cross_minintersections_line = [self.corners3d[idx][self.boxlineidx_xz[intersections_line][0]], self.corners3d[idx][self.boxlineidx_xz[intersections_line][1]]]
                        cross_minintersections_box = self.corners3d[idx]
                        cross_minbox_idx = idx
                        if debug: cross_line2d = [corners2d[idx][self.boxlineidx_xz[intersections_line][0]], corners2d[idx][self.boxlineidx_xz[intersections_line][1]],segpt2d]
                        cross_mindist = dist
                else:
                    if dist < nocross_mindist:
                        nocross_minintersections = [segpt3d, segpt3d]
                        nocross_minintersections_line = [self.corners3d[idx][self.boxlineidx_xz[intersections_line][0]], self.corners3d[idx][self.boxlineidx_xz[intersections_line][1]]]
                        nocross_minintersections_box = self.corners3d[idx]
                        nocross_minbox_idx = idx
                        if debug: nocross_line2d = [corners2d[idx][self.boxlineidx_xz[intersections_line][0]], corners2d[idx][self.boxlineidx_xz[intersections_line][1]],segpt2d]
                        nocross_mindist = dist
        if not match_box_exist_flag: return None, match_box_exist_flag
        if len(cross_minintersections) > 0:
            self.minintersections = cross_minintersections
            self.minintersections_line = cross_minintersections_line
            self.minintersections_box = cross_minintersections_box
            self.minbox_idx = cross_minbox_idx
            if debug: line2d = cross_line2d
            mindist = cross_mindist
        else:
            self.minintersections = nocross_minintersections
            self.minintersections_line = nocross_minintersections_line
            self.minintersections_box = nocross_minintersections_box
            self.minbox_idx = nocross_minbox_idx
            if debug: line2d = nocross_line2d
            mindist = nocross_mindist

        if debug:
            line2d = np.array(line2d) / rate 
            line2d[...,0] += 1080/2 
            line2d[...,1] += height
            cv2.line(self.debug_img, line2d[0].astype('int'), 
                        line2d[1].astype('int'), (255,255,0), thickness=2)
            cv2.circle(self.debug_img,line2d[2].astype('int'),radius=3,color=(0,255,255), thickness=-1)
            gaze2d = gaze2d / rate
            gaze2d[...,0] += 1080/2 
            gaze2d[...,1] += height
            cv2.line(self.debug_img, gaze2d[0].astype('int'), 
                        gaze2d[1].astype('int'), (0,255,255), thickness=2)
            cv2.imwrite('debug.png', self.debug_img)
            # pdb.set_trace()
        return mindist, match_box_exist_flag


    def find_crossPoint(self):
        mindist = np.inf
        self.minintersections = []
        self.minintersections_face = []
        self.minintersections_box = []
        for idx in range(self.corners3d.shape[0]):
            # if idx == 2:
            #     pdb.set_trace()
            dist,intersections,intersections_face = self.ray_cube_intersection(self.origin, self.vector, self.corners3d[idx])
            if dist < mindist:
                self.minintersections = intersections
                self.minintersections_face = intersections_face
                self.minintersections_box = self.corners3d[idx]
                self.minbox_idx = idx
                mindist = dist
        if len(self.minintersections) == 0:
            return False, mindist 
        else:
            return True, mindist
        # pdb.set_trace()


    def plane_from_points(self, p1, p2, p3):
        """
        从三个点计算平面方程 Ax + By + Cz + D = 0 的系数 A, B, C, D
        """
        v1 = p2 - p1
        v2 = p3 - p1
        n = np.cross(v1, v2)
        A, B, C = n
        D = -np.dot(n, p1)
        return A, B, C, D


    def ray_plane_intersection(self, ray_origin, ray_direction, plane):
        """
        计算射线和平面的交点
        """
        A, B, C, D = plane
        ray_origin = np.array(ray_origin)
        ray_direction = np.array(ray_direction)
        denominator = A * ray_direction[0] + B * ray_direction[1] + C * ray_direction[2]
        if np.abs(denominator) < 1e-6:  # 避免除以 0
            return None
        t = - (np.dot(np.array([A, B, C]), ray_origin) + D) / denominator
        if t < 0:  # 交点在射线的反方向
            return None
        intersection = ray_origin + t * ray_direction
        return intersection


    def is_point_in_face(self, intersection, face_vertices):
        """
        检查交点是否在面内
        """
        face_vertices = np.array(face_vertices)
        min_x = min(face_vertices[:, 0])
        max_x = max(face_vertices[:, 0])
        min_y = min(face_vertices[:, 1])
        max_y = max(face_vertices[:, 1])
        min_z = min(face_vertices[:, 2])
        max_z = max(face_vertices[:, 2])
        x, y, z = intersection
        if min_x <= x <= max_x and min_y <= y <= max_y and min_z <= z <= max_z:
            return True
        return False
    
    def is_point_in_convex_quad(self, P, face_vertices):
        A, B, C, D = face_vertices
        # 计算法向量
        N = np.cross(B - A, C - A)
        N = N / np.linalg.norm(N)  # 单位化

        # 定义四边形的点和边
        quad_points = [A, B, C, D]
        edges = [(quad_points[i], quad_points[(i + 1) % 4]) for i in range(4)]

        # 检查点 P 是否在四边形内
        signs = []
        for edge_start, edge_end in edges:
            edge_vector = edge_end - edge_start
            edge_normal = np.cross(N, edge_vector)  # 计算边的法向量
            sign = np.dot(edge_normal, P - edge_start)  # 点到边的方向
            signs.append(sign)

        # 判断所有符号是否一致
        return all(s > 0 for s in signs) or all(s < 0 for s in signs)

    def ray_cube_intersection(self, ray_origin, ray_direction, cube_vertices):
        """
        计算射线和立方体的交点
        """
        faces = [
            [cube_vertices[4], cube_vertices[5], cube_vertices[6], cube_vertices[7]],  # 前面
            [cube_vertices[0], cube_vertices[1], cube_vertices[2], cube_vertices[3]],  # 后面
            [cube_vertices[0], cube_vertices[4], cube_vertices[7], cube_vertices[3]],  # 左面
            [cube_vertices[1], cube_vertices[5], cube_vertices[6], cube_vertices[2]],  # 右面
            [cube_vertices[3], cube_vertices[2], cube_vertices[6], cube_vertices[7]],  # 底面
            [cube_vertices[0], cube_vertices[1], cube_vertices[5], cube_vertices[4]]   # 顶面
        ]
        mindist = np.inf 
        minintersections = []
        minintersections_face = []
        for face in faces:
            plane = self.plane_from_points(face[0], face[1], face[2])
            intersection = self.ray_plane_intersection(ray_origin, ray_direction, plane)
            # if intersection is not None: print(np.linalg.norm(intersection-ray_origin))
            if intersection is not None and self.is_point_in_convex_quad(intersection, face):
                dist = np.linalg.norm(intersection-ray_origin)
                if dist < mindist:
                    minintersections = intersection.tolist()
                    minintersections_face = face
                    mindist = dist

        return mindist,minintersections,minintersections_face

    def project_ray_on_plane(self, ray_origin, ray_direction, plane):
        """
        计算射线在平面上的投影
        """
        A, B, C, D = plane
        ray_origin = np.array(ray_origin)
        ray_direction = np.array(ray_direction)
        n = np.array([A, B, C])
        v_perp = np.dot(ray_direction, n) / np.dot(n, n) * n
        v_parallel = ray_direction - v_perp
        return v_parallel

    def ray_to_face_distance(self, ray_origin, ray_dir, box):
        lines = [
            [box[self.boxlineidx[0][0]],box[self.boxlineidx[0][1]]],#前上 
            [box[self.boxlineidx[1][0]],box[self.boxlineidx[1][1]]],#前下 
            [box[self.boxlineidx[2][0]],box[self.boxlineidx[2][1]]],#前左 
            [box[self.boxlineidx[3][0]],box[self.boxlineidx[3][1]]],#前右 
            [box[self.boxlineidx[4][0]],box[self.boxlineidx[4][1]]],#后上 
            [box[self.boxlineidx[5][0]],box[self.boxlineidx[5][1]]],#后下 
            [box[self.boxlineidx[6][0]],box[self.boxlineidx[6][1]]],#后左 
            [box[self.boxlineidx[7][0]],box[self.boxlineidx[7][1]]],#后右 
            [box[self.boxlineidx[8][0]],box[self.boxlineidx[8][1]]],
            [box[self.boxlineidx[9][0]],box[self.boxlineidx[9][1]]],
            [box[self.boxlineidx[10][0]],box[self.boxlineidx[10][1]]],
            [box[self.boxlineidx[11][0]],box[self.boxlineidx[11][1]]],
        ]
        mindist = np.inf 
        minline = []
        for idx in range(len(lines)):
            raypt, segpt, dist = self.ray_to_segment_distance(ray_origin, ray_dir, lines[idx][0], lines[idx][1])
            if dist < mindist:
                mindist = dist 
                minraypt = raypt 
                minsegpt = segpt 
                minline = idx
        return minraypt, minsegpt, mindist, minline

    def ray_to_line_distance(self, ray_origin, ray_dir, box, box3d):
        lines = [
            [box[self.boxlineidx_xz[0][0]],box[self.boxlineidx_xz[0][1]]],#前上 
            [box[self.boxlineidx_xz[1][0]],box[self.boxlineidx_xz[1][1]]],#前下 
            [box[self.boxlineidx_xz[2][0]],box[self.boxlineidx_xz[2][1]]],#后上
            [box[self.boxlineidx_xz[3][0]],box[self.boxlineidx_xz[3][1]]],#后下 
            [box[self.boxlineidx_xz[4][0]],box[self.boxlineidx_xz[4][1]]], 
            [box[self.boxlineidx_xz[5][0]],box[self.boxlineidx_xz[5][1]]],
            [box[self.boxlineidx_xz[6][0]],box[self.boxlineidx_xz[6][1]]], 
            [box[self.boxlineidx_xz[7][0]],box[self.boxlineidx_xz[7][1]]],
        ]
        lines3d = [
            [box3d[self.boxlineidx_xz[0][0]],box3d[self.boxlineidx_xz[0][1]]],#前上 
            [box3d[self.boxlineidx_xz[1][0]],box3d[self.boxlineidx_xz[1][1]]],#前下 
            [box3d[self.boxlineidx_xz[2][0]],box3d[self.boxlineidx_xz[2][1]]],#后上
            [box3d[self.boxlineidx_xz[3][0]],box3d[self.boxlineidx_xz[3][1]]],#后下 
            [box3d[self.boxlineidx_xz[4][0]],box3d[self.boxlineidx_xz[4][1]]], 
            [box3d[self.boxlineidx_xz[5][0]],box3d[self.boxlineidx_xz[5][1]]],
            [box3d[self.boxlineidx_xz[6][0]],box3d[self.boxlineidx_xz[6][1]]], 
            [box3d[self.boxlineidx_xz[7][0]],box3d[self.boxlineidx_xz[7][1]]],
        ]
        mindist = np.inf 
        minline = []
        crossflag = False
        find_flag = False
        for idx in range(len(lines)):
            segpt2d, segpt3d, dist, crossflag_single = self.ray_to_segment_distance_2d_cross(ray_origin, ray_dir, lines[idx][0], lines[idx][1], lines3d[idx])
            chosen_vec = segpt3d - self.origin
            chosen_vec = chosen_vec / np.linalg.norm(chosen_vec)
            gaze_vec = self.vector
            gaze_vec = gaze_vec / np.linalg.norm(gaze_vec)
            chosen_py = np.array(vec2py(chosen_vec))/np.pi*180
            gaze_py = np.array(vec2py(gaze_vec))/np.pi*180
            diff_py = np.abs(chosen_py - gaze_py)
            # print(dist,diff_py)
            # pdb.set_trace()
            if diff_py[0] < self.config["valid_range"]["pitch"] and diff_py[1] < self.config["valid_range"]["yaw"]:
                valid_range_flag = True 
            else:
                valid_range_flag = False
            # print(dist,crossflag_single)
            # pdb.set_trace()
            # raypt, segpt, dist, crossflag_single = self.ray_to_segment_distance_2d_cross(ray_origin, ray_dir, lines[idx][0], lines[idx][1])
            if dist < mindist and crossflag_single and valid_range_flag:
                mindist = dist 
                minraypt = segpt2d 
                minsegpt = segpt3d 
                minline = idx
                crossflag = True
                find_flag = True
        # pdb.set_trace()
        if crossflag: return minraypt, minsegpt, mindist, minline, crossflag, find_flag
        mindist = np.inf 
        minline = []
        for idx in range(len(lines)):
            segpt2d, segpt3d, dist, crossflag_single = self.ray_to_segment_distance_2d_cross(ray_origin, ray_dir, lines[idx][0], lines[idx][1], lines3d[idx])
            chosen_vec = segpt3d - self.origin
            chosen_vec = chosen_vec / np.linalg.norm(chosen_vec)
            gaze_vec = self.vector
            gaze_vec = gaze_vec / np.linalg.norm(gaze_vec)
            chosen_py = np.array(vec2py(chosen_vec))/np.pi*180
            gaze_py = np.array(vec2py(gaze_vec))/np.pi*180
            diff_py = np.abs(chosen_py - gaze_py)
            if diff_py[0] < self.config["valid_range"]["pitch"] and diff_py[1] < self.config["valid_range"]["yaw"]:
                valid_range_flag = True 
            else:
                valid_range_flag = False
            if dist < mindist and not crossflag_single and valid_range_flag:
                mindist = dist 
                minraypt = segpt2d 
                minsegpt = segpt3d 
                minline = idx
                find_flag = True

        if find_flag: 
            return minraypt, minsegpt, mindist, minline, crossflag, find_flag
        else: 
            return None, None, None, None, crossflag, find_flag

    def ray_to_segment_distance_2d_cross(self, O, d, A, B, seg3d):
        # pdb.set_trace()
        d1, d2 = d 
        e1, e2 = A - B 
        c1, c2 = B - O
        t_ = (d1*c2-d2*c1)/(d2*e1-e2*d1)
        t = (c1 + e1*t_)/d1
        if t < 0:
            OA = np.linalg.norm(O-A)
            OB = np.linalg.norm(O-B)
            if OA > OB:
                return B,seg3d[1],OB,False 
            else:
                return A,seg3d[0],OA,False 
        else:
            closetpt = O + d*t
            if np.dot(A-closetpt,B-closetpt)/(np.linalg.norm(A-closetpt)*np.linalg.norm(B-closetpt)) <= 0:
                rate = np.linalg.norm(A-closetpt)/np.linalg.norm(A-B)
                closetpt3d = seg3d[0] + rate*(seg3d[1]-seg3d[0])
                return closetpt, closetpt3d, np.linalg.norm(closetpt-O), True 
            else:
                def pt2line(O1,D1,A1):
                    d1, d2 = D1
                    c1, c2 = A1-O1
                    t_ = (c2*d1 - d2*c1)/(d1**2+d2**2)
                    t = (c1+d2*t_)/d1
                    # pdb.set_trace()
                    closet = O1 + D1*t
                    dist = np.linalg.norm(closet-A1)
                    return A1, closet, dist
                OA = pt2line(O,d,A)    
                OB = pt2line(O,d,B)   
                if OA[2] < OB[2]:
                    return A, seg3d[0],OA[2],False 
                else:
                    return B, seg3d[1],OB[2],False 

    def ray_to_segment_distance(self, ray_origin, ray_dir, seg_start, seg_end):
        """
        计算射线到线段的最短距离
        :param ray_origin: 射线起点 (3D 点)
        :param ray_dir: 射线方向 (3D 向量，需归一化)
        :param seg_start: 线段起点 (3D 点)
        :param seg_end: 线段终点 (3D 点)
        :return: 最短距离 (float)
        """
        # 线段方向
        # pdb.set_trace()
        ray_dir = ray_dir / np.linalg.norm(ray_dir)
        d = ray_dir  # 射线方向向量
        e = seg_end - seg_start  # 线段方向向量
        w0 = ray_origin - seg_start

        # 内积
        a = np.dot(d, d)  # 射线方向的平方
        b = np.dot(d, e)  # 射线方向与线段方向的点积
        c = np.dot(e, e)  # 线段方向的平方
        d0 = np.dot(d, w0)  # 射线方向与起点差的点积
        e0 = np.dot(e, w0)  # 线段方向与起点差的点积

        # 计算参数 t 和 s
        denom = a * c - b * b  # 行列式
        if denom == 0:  # 射线和线段方向平行
            t = 0
            s = max(0, min(1, e0 / c))  # 将 s 限制在 [0, 1]
        else:
            t = (b * e0 - c * d0) / denom
            s = (a * e0 - b * d0) / denom
            s = max(0, min(1, s))  # 将 s 限制在 [0, 1]

        # 将 t 限制在 t >= 0（射线方向）
        t = max(0, t)

        # 计算最近点
        closest_ray = ray_origin + t * d
        closest_seg = seg_start + s * e
        return closest_ray, closest_seg, np.linalg.norm(closest_ray - closest_seg)



# # 示例使用
# if __name__ == "__main__":

#     # 示例
#     ray_origin = np.array([0, 0, 0])
#     ray_dir = np.array([1, 1, 1]) / np.linalg.norm([1, 1, 1])  # 归一化方向
#     seg_start = np.array([1, 0, 0])
#     seg_end = np.array([1, 1, 0])

#     distance = ray_to_segment_distance(ray_origin, ray_dir, seg_start, seg_end)
#     print("射线到线段的最短距离:", distance)

#     closest_ray_point, closest_seg_point = closest_points_on_ray_and_segment(ray_origin, ray_dir, seg_start, seg_end)
#     print("射线上的最近点:", closest_ray_point)
#     print("线段上的最近点:", closest_seg_point)