
import json 
import numpy as np 
import pdb 
import cv2 
import os 
import onnxruntime 
import math 
import pickle
import copy 
from .utils import FindCrossBox,pred2corners,pt3dto2d
import cv2

class GazeCarModel:
    def __init__(self, modules_setting, camera_path):
        
        self.gazeconfig = modules_setting
        self.enable = self.gazeconfig['enable']
        cam_matrix = json.load(open(camera_path,'r'))
        self.inK = np.array(cam_matrix['IncarCamera']['CameraIntrinsics'])
        self.indist = np.array(cam_matrix['IncarCamera'].get('Distortion',[0.0,0,0,0,0]))
        self.incam_type = cam_matrix['IncarCamera'].get('Camera_type','DMS')
        self.outK = np.array(cam_matrix['OutcarCamera']['CameraIntrinsics'])
        self.outdist = np.array(cam_matrix['OutcarCamera'].get('Distortion',[0.0,0,0,0,0]))
        self.outcam_type = cam_matrix['OutcarCamera'].get('Camera_type','DMS')
        self.in2outMatrix = np.array(cam_matrix['In2OutExtric']['CameraExtric'])
        # pdb.set_trace()
    def run(self, gaze_result, car3d_result,logui_info):
        gazeOrigin_out, gaze_out, car3d_corners = self.preprocess(gaze_result, car3d_result)
        # pdb.set_trace()
        # gt_bboxes_3d_ = np.array(car3d_result['bboxes_3d'])[np.array(car3d_result['scores_3d'])>0.3,:7]
        # findcrossbox = FindCrossBox(gazeOrigin_out,gaze_out,gt_bboxes_3d_)
        findcrossbox = FindCrossBox(gazeOrigin_out,gaze_out,car3d_corners,self.gazeconfig)
        crossflag, mindist = findcrossbox.find_endBox()
        # if isinstance(mindist, tuple):
        #     print(mindist[0])
        # pdb.set_trace()
        if crossflag:
            logui_info['boxRes'] = {'valid':True}
            boxRes = findcrossbox.get_crossRes()
            logui_info['boxRes'].update(boxRes)
        else:
            if isinstance(mindist, tuple):
                logui_info['boxRes'] = {'valid':mindist[1]}
                if mindist[1]:
                    # pdb.set_trace()
                    boxRes = findcrossbox.get_nearestRes()
                    logui_info['boxRes'].update(boxRes)
                else:
                    logui_info['boxRes'] = {'valid':False}
                    boxRes = {}
            else:
                logui_info['boxRes'] = {'valid':True}
                # pdb.set_trace()
                boxRes = findcrossbox.get_nearestRes()
                logui_info['boxRes'].update(boxRes)

        logui_info['Boxes'] = findcrossbox.corners3d
        
        logui_info['RayRes'] = {'gaze_origin': gazeOrigin_out, 'gaze_vec': gaze_out}
        # self.logui_info['gaze_logui']['logui_text_gazevec']
        return boxRes
    
    def preprocess(self, gaze_result, car3d_result):
        # pdb.set_trace()
        chosen_labels = [self.gazeconfig["boxes_label"][i] for i in self.gazeconfig["valid_label"]]
        car3d_corners = []
        for idx,label in enumerate(car3d_result['labels_3d']):
            if label in chosen_labels:
                car3d_corners.append(car3d_result['corners_3d'][idx])
        gazeorigin4d = np.array(gaze_result['gaze_origin'].tolist() + [1])
        gazeorigin4d_out = (self.in2outMatrix @ gazeorigin4d.reshape(4,1)).reshape(-1)
        gazeorigin_out = gazeorigin4d_out[:3] / gazeorigin4d_out[3]
        gaze_out = self.in2outMatrix[:3,:3] @ gaze_result['mix_gaze'].reshape(3,1)
        return gazeorigin_out, gaze_out.reshape(-1), car3d_corners

    def postprocess(self, raw_gaze_res,le_R,re_R,head3d_result,eye_result):
        pass

 