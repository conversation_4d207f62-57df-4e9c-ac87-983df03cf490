import requests
import json
import time

def test_streaming_api_memory():
    """测试内存中处理图片的流式接口"""
    url = "http://localhost:8000/recognize"
    api_key = "sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"
    
    # 测试数据
    test_data = {
        "mix_gaze": "[0.37740352, -0.24775083, -0.89148018]",
        "gaze_origin": "[-29.56759495, -102.30740821, 520.42972422]",
        "gaze_valid": 3,
        "call_jarvis": True,
        "prompt": "这是什么车?",
        "stream": False
    }
    
    # 使用dvr_520.png作为测试图片
    image_path = "dvr_520.png"
    
    try:
        with open(image_path, "rb") as f:
            files = {"outcar_image": (image_path, f, "image/png")}
            headers = {"apl-key": api_key}
            
            print("发送流式请求到gaze接口（内存中处理图片）...")
            start_time = time.time()
            
            # 发送流式请求
            with requests.post(url, files=files, data=test_data, headers=headers, stream=True) as response:
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("开始接收流式响应:")
                    for line in response.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            try:
                                data = json.loads(decoded_line)
                                print(f"收到数据: {data}")
                            except json.JSONDecodeError:
                                print(f"原始数据: {decoded_line}")
                else:
                    print(f"请求失败: {response.text}")
                    
            end_time = time.time()
            print(f"请求总耗时: {end_time - start_time:.2f}秒")
            
    except FileNotFoundError:
        print(f"错误: 找不到图片文件 {image_path}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    test_streaming_api_memory()