{"image_preprocess": [{"op_type": "Det3DDataPreprocessor", "param": {"mean": [103.53, 116.28, 123.675], "std": [1.0, 1.0, 1.0], "bgr_to_rgb": false, "pad_size_divisor": 32}}], "model_postprocess": [{"op_type": "FCOS3D"}], "test_cfg": {"num_classes": 10, "use_direction_classifier": true, "diff_rad_by_sin": true, "pred_attrs": true, "pred_velo": true, "dir_offset": 0.7854, "dir_limit_offset": 0, "strides": [8, 16, 32, 64, 128], "group_reg_dims": [2, 1, 3, 1, 2], "use_rotate_nms": true, "nms_across_levels": false, "nms_pre": 1000, "nms_thr": 0.05, "score_thr": 0.2, "min_bbox_size": 0, "max_per_img": 200}}