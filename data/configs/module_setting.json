{"encrypt": false, "Car3Ddet": {"enable": true, "model_path": "data/models/Car3Ddet/MMDet3d_fcos3d.onnx", "pytorch_model_path": "data/models/Car3Ddet/MMDet3d_fcos3d.pt", "trt_model_path": "data/models/Car3Ddet/MMDet3d_fcos3d.engine", "onnxruntime_custom_op_path": "/home/<USER>/liangpengxiang/WorkSpace/mmdeploy/mmdeploy/lib_1.14.1/libmmdeploy_onnxruntime_ops.so", "tensorrt_custom_op_path": "/home/<USER>/liangpengxiang/WorkSpace/mmdeploy/mmdeploy/lib_tensorrt/libmmdeploy_tensorrt_ops.so", "min_valid_depth": 5.0, "max_valid_depth": 100.0, "debug": false, "enable_track": true, "enable_extrinsics_fix": false}, "GazeCar": {"enable": true, "boxes_label": {"car": 0, "truck": 1, "trailer": 2, "bus": 3, "construction_vehicle": 4, "bicycle": 5, "motocycle": 6, "pedestrian": 7, "traffic_cone": 8, "barrier": 9}, "valid_label": ["car", "truck", "trailer", "bus", "construction_vehicle"], "valid_range": {"pitch": 10, "yaw": 5}}, "logui": {"boxes_label": {"car": 0, "truck": 1, "trailer": 2, "bus": 3, "construction_vehicle": 4, "bicycle": 5, "motocycle": 6, "pedestrian": 7, "traffic_cone": 8, "barrier": 9}, "addition_show_label": ["bicycle", "motocycle", "pedestrian"]}}