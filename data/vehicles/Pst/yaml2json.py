import yaml 
import json 
import numpy as np 
import pdb 
import cv2 
# '''
yamldata = yaml.load(open('./mirror_stereo_pinhole_20250207v3.yaml','r'), Loader=yaml.FullLoader)

jsondata = json.load(open('./camera_info_temp.json','r'))

jsondata['IncarCamera']['CameraIntrinsics'] = yamldata['K_r']
jsondata['IncarCamera']['Distortion'] = np.array(yamldata['dist_r']).reshape(-1).tolist()

jsondata['OutcarCamera']['CameraIntrinsics'] = yamldata['K_l']
jsondata['OutcarCamera']['Distortion'] = np.array(yamldata['dist_l']).reshape(-1).tolist()

jsondata['In2OutExtric']['CameraExtric'] = np.eye(4)
jsondata['In2OutExtric']['CameraExtric'][:3,:3] = np.array(yamldata['R_l_to_r'])

jsondata['In2OutExtric']['CameraExtric'][:3,3:] = np.array(yamldata['T_l_to_r']).reshape(3,1)
R_in2out = np.array([[-9.99998560e-01,  7.14963087e-04, -1.53916727e-03],
       [ 1.11401520e-03,  9.60722439e-01, -2.77508835e-01],
       [ 1.28030397e-03, -2.77510150e-01, -9.60721852e-01]])
T_in2out = np.array([-374.04127872, -282.7781247 ,   33.85521659])
# R_in2out = np.array([[-0.99971063,  0.00767478, -0.02279812],
#        [ 0.01525896,  0.93498463, -0.35435986],
#        [ 0.01859626, -0.35460519, -0.93483118]])
# T_in2out = np.array([-405.18693992, -140.78207695,  296.59982143]) 
# T_in2out = np.array([-354.01419924014806, -229.7978840690953,  511.92602383948724]) 

jsondata['In2OutExtric']['CameraExtric'][:3,:3] = R_in2out.reshape(3,3)
jsondata['In2OutExtric']['CameraExtric'][:3,3:] = T_in2out.reshape(3,1)
# T = np.linalg.inv(jsondata['In2OutExtric']['CameraExtric'])
# rvec = cv2.Rodrigues(T[:3,:3])[0]
# # rvec[1,0] = np.pi
# # rvec[2,0] = 45/180*np.pi
# # rvec[0,0] = 90/180*np.pi
# R = cv2.Rodrigues(rvec)[0]
# T[:3,:3] = R
# # pdb.set_trace()
# jsondata['In2OutExtric']['CameraExtric'] = T.tolist()
jsondata['In2OutExtric']['CameraExtric'] = np.linalg.inv(jsondata['In2OutExtric']['CameraExtric']).tolist()
# jsondata['In2OutExtric']['CameraExtric'] = (jsondata['In2OutExtric']['CameraExtric']).tolist()
json.dump(jsondata,open('./camera_info_20250707.json','w'),indent=4)
# '''
# jsondata = json.load(open('./camera_info_20240212v2.json','r'))
# K = np.array(jsondata['OutcarCamera']['CameraIntrinsics_ori'])
# dist = np.array(jsondata['OutcarCamera']['Distortion'])
# pdb.set_trace()
# new_K = cv2.fisheye.estimateNewCameraMatrixForUndistortRectify(K, dist, (1920,1080), np.eye(3), balance=0, fov_scale=1)
# jsondata['OutcarCamera']['CameraIntrinsics'] = new_K.tolist()
# json.dump(jsondata,open('./camera_info_20240212v2.json','w'),indent=4)
# # pdb.set_trace()