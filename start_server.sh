#!/bin/bash

# 服务启动脚本

# 默认参数
HOST="0.0.0.0"
PORT=8000
USE_HTTPS=false

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

# 设置PYTHONPATH
export PYTHONPATH="${SCRIPT_DIR}:${PYTHONPATH}"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            HOST="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --https)
            USE_HTTPS=true
            shift
            ;;
        --help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --host HOST     指定监听地址 (默认: 0.0.0.0)"
            echo "  --port PORT     指定监听端口 (默认: 8000)"
            echo "  --https         启用HTTPS支持"
            echo "  --help          显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            exit 1
            ;;
    esac
done

# 切换到脚本所在目录
cd "$SCRIPT_DIR"

# 启动服务
if [ "$USE_HTTPS" = true ]; then
    echo "启动HTTPS服务..."
    python recognition_app/server.py --host $HOST --port $PORT --use-https
else
    echo "启动HTTP服务..."
    python recognition_app/server.py --host $HOST --port $PORT
fi