import cv2
import json
import numpy as np
import os 
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
import sys 
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../"))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utils.draw import Draw_logui_info
from modules.build_models import ModelsWarpper
import time

import pdb 
import pickle


if __name__ == "__main__":
    input_config = sys.argv[1]
    output_json = sys.argv[2]

    with open(input_config, "r") as fin:
        DMS_config = json.load(fin)

    cur_time = time.time()
    modelswarpper = ModelsWarpper(
        DMS_config["param_setting_path"],
        DMS_config["module_setting_path"], 
        DMS_config["camera_path"],
        DMS_config["providers"]
    )
    
    print('Initial:',time.time()-cur_time); cur_time = time.time()
    logui = Draw_logui_info(
        {
            'camera_path': DMS_config["camera_path"],
            'save_path': DMS_config["save_path"],
            'module_setting_path':DMS_config["module_setting_path"]
        }
    )

    # img_incar = json.load() # cv2.imread(DMS_config["Incar_img_path"])
    img_incar = cv2.imread(DMS_config["Incar_img_path"])
    img_outcar = cv2.imread(DMS_config["Outcar_img_path"])
    # pdb.set_trace()
    pickle_save_path = logui.img_save_path + '_pickle'
    os.makedirs(pickle_save_path,exist_ok=True)
    car3ddet_save_path = logui.img_save_path + '_car3ddet_json'
    os.makedirs(car3ddet_save_path,exist_ok=True)

    framenum = 0
    # 处理当前帧
    # test_pipeline
    modelswarpper.run(img_incar, img_outcar, 0)
    models_result = modelswarpper.get_logui_info()

    cur_time = time.time()
    pickle.dump(models_result, open(os.path.join(pickle_save_path,'{}.pickle'.format(framenum)),'wb'))

    if modelswarpper.car_bboxes is not None:
        with open(os.path.join(car3ddet_save_path, f'{framenum}.json'), "w") as fout:
            json.dump(modelswarpper.car_bboxes, fout, indent=2)
        
    logui(img_incar, framenum, models_result)
    print('logui:', time.time() - cur_time); cur_time = time.time()
    output_result = modelswarpper.get_output_info()
    # pdb.set_trace()
    ## debug 
    # pts = output_result['GazeSelected'][0]['2DRect']
    # pts = output_result['GazeSelected'][0]['Undist2DRect']
    # cv2.rectangle(img_outcar, [pts[0],pts[1]], [pts[2],pts[3]], (0,0,255), thickness=1)
    # undistimg = models_result['frame_outcar_undist']
    # cv2.rectangle(undistimg, [pts[0],pts[1]], [pts[2],pts[3]], (0,0,255), thickness=1)
    print(output_result["GazeSelected"])
    with open(output_json, "w") as fout:
        json.dump(output_result, fout, indent=2)

    logui.release()
