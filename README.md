# OutCabin Vehicle Recognition API

基于 gaze 检测和 JARVIS AI 的车辆识别 API 服务，支持通过视线追踪定位车辆并进行智能识别。

## 功能特性

- 🚗 **车辆检测**: 基于 gaze 信息精确定位图像中的车辆
- 🤖 **AI 识别**: 集成 JARVIS API 进行车辆型号、品牌等智能识别
- 📍 **坐标计算**: 提供精确的 2D 坐标和检测框信息
- 🆔 **请求追踪**: 返回 JARVIS API 的唯一 ID 用于请求追踪
- ⚡ **灵活控制**: 支持选择性调用 AI 识别功能
- 🌊 **流式输出**: 支持实时流式返回 AI 识别结果
- 🔒 **安全认证**: API 密钥认证机制

## 快速开始

### 环境要求

- Python 3.9+
- OpenCV
- FastAPI
- Uvicorn

### 安装依赖

```bash
conda activate outcabin_py3.9
pip install fastapi uvicorn opencv-python requests
```

### 启动服务

```bash
python -m uvicorn recognition_app.main:app --host 0.0.0.0 --port 8000
```

服务启动后访问: http://localhost:8000

## API 接口

### POST /recognize

车辆识别接口，支持基于 gaze 信息的车辆检测和 AI 识别。

#### 请求参数

| 参数           | 类型    | 必需 | 默认值  | 描述                         |
| -------------- | ------- | ---- | ------- | ---------------------------- |
| `outcar_image` | File    | ✅   | -       | 车辆图片文件                 |
| `mix_gaze`     | String  | ✅   | -       | gaze 方向向量，JSON 数组格式 |
| `gaze_origin`  | String  | ✅   | -       | gaze 起点坐标，JSON 数组格式 |
| `gaze_valid`   | Integer | ✅   | -       | gaze 有效性标志              |
| `prompt`       | String  | ❌   | `""`    | AI 识别提示词                |
| `call_jarvis`  | Boolean | ❌   | `false` | 是否调用 JARVIS AI 识别      |
| `stream`       | Boolean | ❌   | `false` | 是否使用流式返回 AI 识别结果 |

#### 请求头

| 参数      | 类型   | 必需 | 描述         |
| --------- | ------ | ---- | ------------ |
| `apl-key` | String | ✅   | API 认证密钥 |

#### 响应格式

**普通模式 (stream=false)**

```json
{
  "code": 0,
  "output": "JARVIS识别结果或null",
  "itemID": "检测到的车辆ID",
  "2DRect_ori": [x1, y1, x2, y2],
  "2DRects": [[x1, y1, x2, y2], ...],
  "gaze_point": [x, y],
  "id": "jarvis_api_id_or_null",
  "error_msg": "OK或错误信息"
}
```

**流式模式 (stream=true)**

Content-Type: `text/event-stream`

```
data: {"code": 0, "itemID": "car1", "2DRect_ori": [236, 457, 552, 653], "2DRects": [[236, 457, 552, 653]], "gaze_point": [305.07, 460.75], "output": "这是", "jarvis_id": "uuid", "stream_state": "generating"}

data: {"code": 0, "itemID": "car1", "2DRect_ori": [236, 457, 552, 653], "2DRects": [[236, 457, 552, 653]], "gaze_point": [305.07, 460.75], "output": "一辆", "jarvis_id": "uuid", "stream_state": "generating"}

data: {"code": 0, "itemID": "car1", "2DRect_ori": [236, 457, 552, 653], "2DRects": [[236, 457, 552, 653]], "gaze_point": [305.07, 460.75], "output": "完整的识别结果", "jarvis_id": "uuid"}
```

#### 响应字段说明

**普通模式字段**

| 字段         | 类型         | 描述                                             |
| ------------ | ------------ | ------------------------------------------------ |
| `code`       | Integer      | 状态码，0 表示成功                               |
| `output`     | String\|null | **仅用于 JARVIS API 返回结果**，其他情况为 null  |
| `itemID`     | String       | 检测到的车辆项目 ID                              |
| `2DRect_ori` | Array        | 原始检测框坐标 [x1, y1, x2, y2]                  |
| `2DRects`    | Array        | 所有检测框列表                                   |
| `gaze_point` | Array        | gaze 点在图像中的坐标 [x, y]                     |
| `id`         | String\|null | JARVIS API 返回的唯一 ID，未调用时为 null        |
| `error_msg`  | String       | 执行状态：成功时为"OK"，否则为具体错误或跳过原因 |

**流式模式额外字段**

| 字段           | 类型   | 描述                                                |
| -------------- | ------ | --------------------------------------------------- |
| `jarvis_id`    | String | JARVIS API 返回的唯一 ID（流式模式中的字段名）      |
| `stream_state` | String | 流式状态："generating" 表示生成中，无此字段表示完成 |

**流式模式说明**

- 每个 `data:` 行包含一个 JSON 对象
- 流式过程中，`output` 字段包含当前生成的文本片段
- 最后一个数据包不包含 `stream_state` 字段，`output` 为完整结果
- 车辆检测信息（检测框、gaze 点等）在第一个数据包中返回

## 使用示例

### cURL 示例

#### 基础车辆检测（不调用 AI）

```bash
curl -X POST http://localhost:8000/recognize \
  -H "apl-key: your-api-key" \
  -F "outcar_image=@car.jpg" \
  -F "mix_gaze=[0.37740352, -0.24775083, -0.89148018]" \
  -F "gaze_origin=[-29.56759495, -102.30740821, 520.42972422]" \
  -F "gaze_valid=3"
```

#### 完整 AI 识别

```bash
curl -X POST http://localhost:8000/recognize \
  -H "apl-key: your-api-key" \
  -F "outcar_image=@car.jpg" \
  -F "mix_gaze=[0.37740352, -0.24775083, -0.89148018]" \
  -F "gaze_origin=[-29.56759495, -102.30740821, 520.42972422]" \
  -F "gaze_valid=3" \
  -F "call_jarvis=true" \
  -F "prompt=这是什么车"
```

#### 流式 AI 识别

```bash
curl -X POST http://localhost:8000/recognize \
  -H "apl-key: your-api-key" \
  -H "Accept: text/event-stream" \
  -F "outcar_image=@car.jpg" \
  -F "mix_gaze=[0.37740352, -0.24775083, -0.89148018]" \
  -F "gaze_origin=[-29.56759495, -102.30740821, 520.42972422]" \
  -F "gaze_valid=3" \
  -F "call_jarvis=true" \
  -F "stream=true" \
  -F "prompt=这是什么车"
```

### Python 示例

#### 普通请求

```python
import requests

# API配置
url = "http://localhost:8000/recognize"
headers = {"apl-key": "your-api-key"}

# 准备数据
files = {"outcar_image": open("car.jpg", "rb")}
data = {
    "mix_gaze": "[0.37740352, -0.24775083, -0.89148018]",
    "gaze_origin": "[-29.56759495, -102.30740821, 520.42972422]",
    "gaze_valid": 3,
    "call_jarvis": True,
    "prompt": "这是什么车"
}

# 发送请求
response = requests.post(url, headers=headers, files=files, data=data)
result = response.json()

print(f"执行状态: {result['error_msg']}")
print(f"识别结果: {result['output']}")
print(f"检测框: {result['2DRect_ori']}")
print(f"gaze点: {result['gaze_point']}")
print(f"JARVIS ID: {result['id']}")
```

#### 流式请求

```python
import requests
import json

# API配置
url = "http://localhost:8000/recognize"
headers = {
    "apl-key": "your-api-key",
    "Accept": "text/event-stream"
}

# 准备数据
files = {"outcar_image": open("car.jpg", "rb")}
data = {
    "mix_gaze": "[0.37740352, -0.24775083, -0.89148018]",
    "gaze_origin": "[-29.56759495, -102.30740821, 520.42972422]",
    "gaze_valid": 3,
    "call_jarvis": True,
    "stream": True,
    "prompt": "这是什么车"
}

# 发送流式请求
response = requests.post(url, headers=headers, files=files, data=data, stream=True)

if response.status_code == 200:
    print("开始接收流式数据:")
    for line in response.iter_lines(decode_unicode=True):
        if line and line.startswith("data: "):
            data_str = line[6:]  # 去掉 "data: " 前缀
            chunk_data = json.loads(data_str)

            # 检查是否是最终结果
            if 'stream_state' not in chunk_data:
                print(f"\n最终结果: {chunk_data['output']}")
                break
            else:
                # 实时显示流式内容
                print(chunk_data['output'], end='', flush=True)
```

### JavaScript 示例

```javascript
const formData = new FormData();
formData.append("outcar_image", fileInput.files[0]);
formData.append("mix_gaze", "[0.37740352, -0.24775083, -0.89148018]");
formData.append("gaze_origin", "[-29.56759495, -102.30740821, 520.42972422]");
formData.append("gaze_valid", "3");
formData.append("call_jarvis", "true");
formData.append("prompt", "这是什么车");

fetch("http://localhost:8000/recognize", {
  method: "POST",
  headers: {
    "apl-key": "your-api-key",
  },
  body: formData,
})
  .then((response) => response.json())
  .then((data) => {
    console.log("执行状态:", data.error_msg);
    console.log("识别结果:", data.output);
    console.log("JARVIS ID:", data.id);
  });
```

## 功能说明

### Gaze 检测

系统使用 gaze 信息（视线追踪数据）来定位图像中的目标车辆：

- `mix_gaze`: 视线方向向量，3D 坐标
- `gaze_origin`: 视线起点，3D 坐标
- `gaze_valid`: 视线数据有效性标志

### AI 识别控制

通过参数控制是否调用 JARVIS AI 进行车辆识别：

- `call_jarvis=false`: 仅进行车辆检测，返回坐标信息
- `call_jarvis=true` + `prompt`: 调用 AI 识别，返回详细车辆信息

### 流式输出

支持实时流式返回 AI 识别结果：

- `stream=false`: 普通模式，等待完整结果后返回
- `stream=true`: 流式模式，实时返回 AI 生成的内容片段
- 流式模式仅在 `call_jarvis=true` 且 `prompt` 不为空时生效
- 返回格式为 Server-Sent Events (SSE)，Content-Type: `text/event-stream`

### 调用条件

JARVIS AI 只有在以下条件同时满足时才会被调用：

1. `call_jarvis = true`
2. `prompt` 不为空且不为纯空格

### 响应字段详解

#### output 字段规则

- **成功调用 JARVIS API**: 包含 AI 识别的具体结果
- **未调用 JARVIS API**: 设为 `null`
- **发生错误**: 设为 `null`

#### error_msg 字段规则

- **正常执行**: `"OK"`
- **未启用 AI**: `"未启用车辆识别"`
- **prompt 为空**: `"prompt为空，跳过车辆识别"`
- **发生错误**: 具体的错误描述信息

#### 判断执行状态

```javascript
// 判断是否成功
if (response.code === 0) {
  if (response.error_msg === "OK") {
    // 成功调用了 JARVIS API
    console.log("AI识别结果:", response.output);
  } else {
    // 成功执行但未调用 JARVIS API
    console.log("跳过原因:", response.error_msg);
  }
} else {
  // 发生错误
  console.log("错误信息:", response.error_msg);
}
```

## 错误码

| 错误码 | 说明                |
| ------ | ------------------- |
| 0      | 成功                |
| 1001   | 图片读取失败        |
| 1002   | 图片加载失败        |
| 1003   | 图片裁剪失败        |
| 1004   | JARVIS API 请求失败 |
| 1005   | 响应解析失败        |
| 1006   | 格式错误            |
| 1007   | 未检测到 gaze       |

## 测试工具

项目提供了完整的测试工具：

### Python 测试脚本

```bash
# 完整功能测试（包含流式和非流式对比）
python test_streaming.py

# 简化流式测试
python test_streaming_simple.py

# 基础API测试
python test_gaze_api.py
```

### Bash 测试脚本

```bash
chmod +x test_gaze_curl.sh
./test_gaze_curl.sh
```

## 部署说明

### 生产环境

```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker recognition_app.main:app --bind 0.0.0.0:8000

# 或使用Docker
docker build -t outcabin-api .
docker run -p 8000:8000 outcabin-api
```

### 环境变量

```bash
export API_KEY="your-jarvis-api-key"
export JARVIS_API_URL="https://jarvis.senseauto.com:1050/v1/2024_byd_poc"
```

## 性能优化

- 使用异步处理提高并发性能
- 支持图片格式自动转换
- 智能错误处理和重试机制
- 内存优化的图片处理

## 日志系统

### 日志格式

系统使用标准的 Python logging 模块，日志格式为：

```
2025-08-05 16:51:59 - recognition_app.recognizer - INFO - 模型初始化完成
```

### 日志级别

- **INFO**: 一般信息，如请求处理、模型初始化等
- **WARNING**: 警告信息，如响应格式异常等
- **ERROR**: 错误信息，如文件读取失败、API 调用失败等
- **DEBUG**: 调试信息，如详细的坐标计算过程

### 日志配置

可以通过修改 logging 配置来调整日志级别：

```python
import logging
logging.getLogger('recognition_app').setLevel(logging.DEBUG)
```

## 安全考虑

- API 密钥认证
- 文件类型验证
- 请求大小限制
- 错误信息脱敏

## 响应示例

### 成功响应（调用 AI）

```json
{
  "code": 0,
  "output": "这是一辆荣威Ei5。",
  "itemID": "car1",
  "2DRect_ori": [236, 457, 552, 653],
  "2DRects": [
    [236, 457, 552, 653],
    [476, 492, 668, 604]
  ],
  "gaze_point": [305, 460],
  "id": "cd47a9c5-a077-44ac-a7ba-1b7e84958055",
  "error_msg": "OK"
}
```

### 成功响应（未启用 AI）

```json
{
  "code": 0,
  "output": null,
  "itemID": "car1",
  "2DRect_ori": [236, 457, 552, 653],
  "2DRects": [
    [236, 457, 552, 653],
    [476, 492, 668, 604]
  ],
  "gaze_point": [305, 460],
  "id": null,
  "error_msg": "未启用车辆识别"
}
```

### 成功响应（prompt 为空）

```json
{
  "code": 0,
  "output": null,
  "itemID": "car1",
  "2DRect_ori": [236, 457, 552, 653],
  "2DRects": [
    [236, 457, 552, 653],
    [476, 492, 668, 604]
  ],
  "gaze_point": [305, 460],
  "id": null,
  "error_msg": "prompt为空，跳过车辆识别"
}
```

### 错误响应

```json
{
  "code": 1002,
  "output": null,
  "error_msg": "无法加载图片文件"
}
```

## 更新日志

### v2.2.0 (最新)

- ✅ **修复流式接口**: 完全修复流式返回功能，支持实时 AI 识别结果
- ✅ **优化流式性能**: 使用内存版本的流式处理，避免文件 I/O 问题
- ✅ **完善测试工具**: 提供专门的流式测试脚本和逐字打印效果
- ✅ **改进用户体验**: 流式输出支持真实的实时显示效果
- ✅ **更新文档**: 完善流式接口的使用说明和示例代码

### v2.1.0

- ✅ **新增 error_msg 字段**: 统一错误信息和状态反馈
- ✅ **规范化 output 字段**: 仅用于 JARVIS API 返回结果
- ✅ **优化日志格式**: 使用标准 Python logging，移除 emoji
- ✅ **改进错误处理**: 更清晰的错误信息分类
- ✅ **完善响应格式**: 所有情况下都有明确的状态信息

### v2.0.0

- ✅ 添加 JARVIS AI 识别功能
- ✅ 支持选择性 AI 调用
- ✅ 返回 JARVIS API 唯一 ID
- ✅ 优化错误处理机制

### v1.0.0

- ✅ 基础 gaze 检测功能
- ✅ 车辆坐标定位
- ✅ RESTful API 接口

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
