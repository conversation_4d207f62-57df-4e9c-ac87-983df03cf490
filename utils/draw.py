import open3d as o3d
import numpy as np
import pdb 
import cv2
import json 
import copy 
import time
from datetime import datetime
import os 
import math 

def pt3dto2d(corners, K, dist):
    # points_4 = np.concatenate([corners, np.ones((*corners.shape[:-1],1))], axis=-1)
    # proj_mat = np.eye(4)
    # proj_mat[:3,:3] = K
    # point_2d = points_4 @ proj_mat.T
    # point_2d_res = point_2d[..., :2] / point_2d[..., 2:3]
    # point_2d_res = np.round(point_2d_res - 1)
    point_2d_res, _ = cv2.projectPoints(corners, np.zeros((3,1)), np.zeros((3,1)),K,dist)
    # imgfov_pts_2d = point_2d_res.reshape(-1,8,2)
    return np.round(point_2d_res).astype('int')

def fit_plane_least_squares(points):
    """
    使用最小二乘法拟合 3D 平面
    :param points: 输入的 3D 点列表，形状为 (N, 3) 的 numpy 数组
    :return: 平面的法向量 (a, b, c) 和 d
    """
    points = np.array(points)
    A = np.c_[points[:, 0], points[:, 1], np.ones(points.shape[0])]
    b = -points[:, 2]
    x, _, _, _ = np.linalg.lstsq(A, b, rcond=None)
    a, b, d = x
    c = 1
    n = np.array([a, b, c])
    d = -d
    return n, d

def fit_plane(points):
    """
    拟合一个平面到给定的 3D 点。
    :param points: n x 3 的 numpy 数组，每行表示一个点的 (x, y, z) 坐标。
    :return: 平面法向量 (a, b, c) 和偏移 d。
    """
    # 提取点的坐标
    x = points[:, 0]
    y = points[:, 1]
    z = points[:, 2]
    
    # 构造 A 矩阵和 b 向量
    A = np.c_[x, y, np.ones(points.shape[0])]  # A = [x, y, 1]
    b = z  # b = z
    
    # 使用最小二乘法求解
    coeff, _, _, _ = np.linalg.lstsq(A, b, rcond=None)  # coeff = [a, b, d]
    
    # 提取法向量和偏移
    a, b, d = coeff
    normal = np.array([a, b, -1])  # 平面的法向量
    normal = normal / np.linalg.norm(normal)  # 归一化法向量
    
    return normal, d

def plv2(points):
    centroid = points.mean(axis=0)  # 计算中心点
    normalized_points = points - centroid  # 点减去中心点
    _, _, vh = np.linalg.svd(normalized_points)  # 奇异值分解
    normal_vector = vh[-1]  # 最小奇异值对应的右奇异向量是法向量

    # 计算平面方程的常数项 d
    d = -np.dot(normal_vector, centroid)
    return normal_vector, d

def eulerAngles2rotationMat(theta, sequence='zyx'):
    """
    Calculates Rotation Matrix given euler angles.
    :param theta: 1-by-3 list [rx, ry, rz] angle in degree
    :return:
    RPY角，是ZYX欧拉角，依次 绕定轴XYZ转动[rx, ry, rz]
    """
    theta = [i * math.pi / 180.0 for i in theta]

    R_x = np.array([[1, 0, 0],
                    [0, math.cos(theta[0]), -math.sin(theta[0])],
                    [0, math.sin(theta[0]), math.cos(theta[0])]
                    ])

    R_y = np.array([[math.cos(theta[1]), 0, math.sin(theta[1])],
                    [0, 1, 0],
                    [-math.sin(theta[1]), 0, math.cos(theta[1])]
                    ])

    R_z = np.array([[math.cos(theta[2]), -math.sin(theta[2]), 0],
                    [math.sin(theta[2]), math.cos(theta[2]), 0],
                    [0, 0, 1]
                    ])
    
    R = np.dot(R_z, np.dot(R_y, R_x))

    if sequence == 'xyz' :
        R = np.dot(R_x, np.dot(R_y, R_z))

    return R

def render_bfm(image, 
                vertices, 
                meanface_Z,
                triangles, 
                num_tri,
                h,
                w):
    depth_buffer = np.zeros((h, w))
    meanface_Z = 1 - (meanface_Z - min(meanface_Z))/(max(meanface_Z) - min(meanface_Z))
    
    sort_indices = np.argsort(meanface_Z[triangles[:,0]])
    
    triangles = triangles[sort_indices]
    
    value_min = 1000
    value_max = -1
    
    # ipdb.set_trace()
    channels = len(image.shape)
    if channels != 2:
        image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

    for i in range(num_tri):
        tri_idxs = triangles[i]
        p_list = [vertices[tri_idx]  for tri_idx in tri_idxs]
        Z_list = [meanface_Z[tri_idx]  for tri_idx in tri_idxs]

        x_min = max(math.floor(min(p_list[0][0], p_list[1][0], p_list[2][0])), 0)
        x_max = min(math.ceil(max(p_list[0][0], p_list[1][0], p_list[2][0])), w - 1)

        y_min = max(math.floor(min(p_list[0][1], p_list[1][1], p_list[2][1])), 0)
        y_max = min(math.ceil(max(p_list[0][1], p_list[1][1], p_list[2][1])), h - 1)

        x_center = (x_min + x_max) // 2
        y_center = (y_min + y_max) // 2
        
        points = np.array(p_list, dtype=np.int32)
        points = points.reshape((-1, 1, 2))
        
        pix_value = int(Z_list[0]*100+50)
        
        cv2.fillPoly(depth_buffer, [points], color=pix_value)
     
    mask = depth_buffer > 0

    image[mask] = depth_buffer[mask]
    
    return image

def load_obj(obj_name):
    f_dict = {'nose': [],
              'eyes': [],
              'lips': [],
              'brows': [],
              'front': [],
              'side': []}
    with open(obj_name, 'r') as fid:
        lines = fid.readlines()
    v = [];
    tri = []
    for l in lines:
        l = [_ for _ in l.strip().split(' ') if len(_) > 0]
        if len(l) == 2 and l[1] in f_dict.keys():
            sign = l[1]
        if len(l) >= 3 and l[0] == 'v':
            v += [[float(_) for _ in l[1:]]]
        elif len(l) >= 4 and l[0] == 'f':
            f = [[], [], []]
            for i in range(1, len(l)):
                s = l[i].split('/')
                for j in range(3):
                    if j < len(s) and len(s[j]) > 0:
                        f[j] += [int(s[j]) - 1]
            tmp = [[f[0][0], f[0][i - 1], f[0][i]] for i in range(2, len(f[0]))]
            tri += tmp
            # f_dict[sign].append(tmp)
    v = np.array(v, np.float64)
    tri = np.array(tri, np.int64)
    return v, tri, f_dict

def get_v(path_obj, visualize=False):
    v, tri, f_dict = load_obj(path_obj)
    meanface = v*-10  ## 需要调整下scale尺寸
    # pdb.set_trace()
    return meanface, tri

path_obj = "./data/configs/simplified_model_1589.obj"
meanface, tri = get_v(path_obj)
def obj_draw(img, angle, shift_x, shift_y, T, K = np.array([[1406., 0, 628],[0, 1423, 389],[0, 0, 1]])):
    """headpose 渲染函数

    Args:
        img: 输入图像
        angle: headpose 数值，依次为 yaw,pitch,roll
        shift_x: obj obj在图片的像素偏移
        shift_y: obj obj在图片的像素偏移
        T: head3d 数值, 3*4矩阵

    Returns:
        img 渲染图像(灰度图)
    """

    K[0][2] = K[0][2] + shift_x
    K[1][2] = K[1][2] + shift_y

    dist = None

    yaw = angle[0]
    pitch = angle[1]
    roll = angle[2]

    headpose = np.array([-pitch, yaw, -roll])

    R = eulerAngles2rotationMat(headpose, 'xyz')


    meanface_R = np.dot(R, meanface.T).T[:,-1]

    r = cv2.Rodrigues(R)[0]

    translation_vector = np.array(T[:3,3])
    (point_2d, _) = cv2.projectPoints(meanface,
                                      r,
                                      translation_vector,
                                      K,  # 相机内参矩阵
                                      dist)  # 相机畸变
  
    point_2d = np.int32(point_2d.reshape(-1, 2))

    img = render_bfm(img, point_2d, meanface_R, tri, tri.shape[0], img.shape[0], img.shape[1])
    return img

def fit_plane(points):
    """
    给定 3D 空间中的点，拟合离这些点距离最近的平面
    """
    # 提取 x, y, z 坐标
    X = points[:, [0,2]]  # 提取 x 和 z 坐标
    Y = points[:, 1]   # 提取 z 坐标

    # 在 X 中添加一列常数项 1
    X = np.hstack((X, np.ones((X.shape[0], 1))))

    # 最小二乘法求解平面参数
    # Z = X @ [a, b, c]
    coef = np.linalg.lstsq(X, Y, rcond=None)[0]

    # 平面方程系数 a, b, c
    a, b, c = coef
    return a, b, c

class Draw_logui_info:
    def __init__(self, config, fps = 15):
        # pdb.set_trace(c)
        
        self.cam_matrix = json.load(open(config['camera_path'],'r'))
        self.inK = np.array(self.cam_matrix['IncarCamera']['CameraIntrinsics'])
        self.indist = np.array(self.cam_matrix['IncarCamera'].get('Distortion',[0.0,0,0,0,0]))
        self.incam_type = self.cam_matrix['IncarCamera'].get('Camera_type','DMS')
        self.outK = np.array(self.cam_matrix['OutcarCamera']['CameraIntrinsics'])
        # self.outdist = np.array(self.cam_matrix['OutcarCamera'].get('Distortion',[0.0,0,0,0,0]))
        self.outdist = np.array([0.0,0,0,0,0])
        self.outcam_type = self.cam_matrix['OutcarCamera'].get('Camera_type','DMS')
        self.in2outMatrix = np.array(self.cam_matrix['In2OutExtric']['CameraExtric'])
        if 'module_setting_path' in config:
            self.logui_config = json.load(open(config['module_setting_path'],'r')).get('logui',{})
        else:
            self.logui_config = {}

        self.frame_height, self.frame_width = 720, 1080
        if 'save_path' in config:
            save_path = config['save_path']
            if save_path.split('.')[-1].lower() not in ['mp4','avi']:
                save_path += '_.mp4'
        else:
            raise TypeError('Please set save_path name end with .mp4 or .avi')
        idx = 0
        while save_path[0] in ['/','.']:
            idx += 1
            save_path = save_path[1:]
            if idx > 10:
                break
        save_path = os.path.join('./results',save_path)
        # pdb.set_trace()
        self.img_save_path = save_path.rsplit('.',1)[0]
        try:
            os.makedirs(os.path.dirname(save_path),exist_ok=True)
        except:
            pass
        os.makedirs(self.img_save_path,exist_ok=True)
        if save_path.split('.')[-1].lower() == 'avi':
            fourcc = cv2.VideoWriter_fourcc(*'MJPG')
        elif save_path.split('.')[-1].lower() == 'mp4':
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        else:
            raise TypeError('Please set save_path name end with .mp4 or .avi')
        
        
        self.logui_save = cv2.VideoWriter(save_path, fourcc, fps, (self.frame_width*3, self.frame_height))

    def release(self):
        self.logui_save.release()

    def __call__(self, framenum, logui_info):
        outframe = logui_info['frame_outcar_undist']
        self.logui_info = logui_info
        # if logui_info['car_bboxes'] and logui_info['face_bbox']:
        #     inframesave = cv2.resize(self.incarShow(inframe, framenum), (self.frame_width,self.frame_height))
        # else:
        #     self.inframe = inframe
        #     self.wordsize = [0, 25]
        #     self.text_shift = [10, 25]
        #     self.draw_text("Frame: {}".format(framenum))
        #     self.draw_text("car_bboxes: {}".format(logui_info['car_bboxes']))
        #     self.draw_text("face_bbox: {}".format(logui_info['face_bbox']))
        #     inframesave = cv2.resize(self.inframe, (self.frame_width,self.frame_height))
        # if logui_info['car_bboxes']:
        #     outframesave = cv2.resize(self.show2d(outframe), (self.frame_width,self.frame_height))
        #     outframe3dsave = cv2.resize(self.show3dto2d(), (self.frame_width,self.frame_height))
        # else:
        #     outframesave = cv2.resize(outframe, (self.frame_width,self.frame_height))
        #     outframe3dsave = cv2.resize(np.zeros_like(outframe).astype('uint8'), (self.frame_width,self.frame_height))
        # # pdb.set_trace()
        # saveframe = np.concatenate([inframesave,outframesave,outframe3dsave],axis=1)
        # self.logui_save.write(saveframe)
        # cv2.imwrite(os.path.join(self.img_save_path,'{:06d}.png'.format(framenum)),saveframe)

        if logui_info['car_bboxes']:
            cv2.imwrite("tt0.png", outframe)
            outframesave = cv2.resize(self.show2d(outframe), (self.frame_width,self.frame_height))
            cv2.imwrite('tt.jpg',outframesave)
    
    def incarShow(self, frame, framenum):
        self.inframe = frame
        self.wordsize = [0, 25]
        self.text_shift = [10, 25]
        # self.logui_info = logui_info

        self.draw_text("Frame: {}".format(framenum))
        for fun_key in self.logui_info.keys():
            if fun_key in ['Wholebody_logui','headpose','head3d','gaze_logui']:
                print("Draw fun {} logui".format(fun_key))
                method = getattr(self, fun_key)
                method(self.logui_info[fun_key])
        # pdb.set_trace()
        return self.inframe
    
    def draw_text(self, casetxt):
        if self.text_shift[1] > self.frame_height-25:
            self.text_shift[1] = 25
            self.text_shift[0] = self.wordsize[0]+50
            self.wordsize[0] = 0
        cv2.putText(self.inframe, casetxt, (self.text_shift[0], self.text_shift[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,0), 2, 1)
        self.text_shift[1] += self.wordsize[1]
        self.wordsize[0] = max(self.wordsize[0],len(casetxt))

    def pts3dto2d(self, pts):
        pts = np.array(pts).astype('float32')
        K = np.array([[self.viewpoint_f, 0, self.frame_width/2], 
              [0, self.viewpoint_f, self.frame_height/2], 
              [0, 0, 1]], dtype=np.float32)
        # pdb.set_trace()
        image_points, _ = cv2.projectPoints(pts, self.viewpoint_R, self.viewpoint_t, K, distCoeffs=None)
        return image_points[:,0,:].astype('int')
    
    def show3dto2d(self):

        self.viewpoint_t = np.array([0,0,1500]).reshape(3,1).astype('float32')
        self.viewpoint_R = np.array([30/180*np.pi, 0, 0]).reshape(3,1).astype('float32')
        # self.viewpoint_T = np.eye(4)
        # self.viewpoint_T[:3,:3] = viewpoint_R
        # self.viewpoint_T[3:,:3] = viewpoint_t
        # ptslist = self.logui_info['Boxes'].reshape(-1,3).tolist()
        # if self.logui_info['face_bbox']: ptslist.append(self.logui_info['RayRes']['gaze_origin'].tolist())
        # pts = np.array(ptslist).astype('float32')
        # K = np.eye(3).astype('float32')
        # image_points, _ = cv2.projectPoints(pts, self.viewpoint_R, self.viewpoint_t, K, distCoeffs=None)
        # rangex = np.max(np.abs(image_points[:,0,0])) * 2
        # rangey = np.max(np.abs(image_points[:,0,1])) * 2
        # self.viewpoint_f = min(2/3*self.frame_width/rangex, 2/3*self.frame_height/rangey)
        self.img3d = np.ones((self.frame_height, self.frame_width,3)).astype('uint8')*255
        self.viewpoint_f = self.outK[0,0] / self.logui_info['frame_outcar'].shape[0] * self.frame_height / 2
        # pdb.set_trace()
        ## 绘制地板
        if self.logui_info['face_bbox']:
            if 'VanishY' in self.cam_matrix['OutcarCamera']:
                self.getGround3dto2d_VanishY()
            else:
                self.getGround3dto2d(self.logui_info['Boxes'].reshape(-1,3),self.logui_info['RayRes']['gaze_origin'])
        else:
            self.getGround3dto2d(self.logui_info['Boxes'].reshape(-1,3),[0,0,0])

        # pdb.set_trace()
        ## 绘制车辆框
        for idx in range(self.logui_info['Boxes'].shape[0]):
            # pdb.set_trace()
            box_corners = self.logui_info['Boxes'][idx]
            box_corners_2d = self.pts3dto2d(box_corners)
            if self.logui_info['face_bbox'] and self.logui_info['boxRes']['valid']:
                if idx == self.logui_info['boxRes']['box_idx']:
                    self.img3d = self.draw_box2d(self.img3d, box_corners_2d,[0,255,0])
                else:
                    self.img3d = self.draw_box2d(self.img3d, box_corners_2d)
            else:
                self.img3d = self.draw_box2d(self.img3d, box_corners_2d)
        ## 绘制附加信息
        if 'addition_show_label' in self.logui_config:
            chosen_labels = [self.logui_config["boxes_label"][i] for i in self.logui_config["addition_show_label"]]
            addition_corners = []
            for idx,label in enumerate(self.logui_info['car_bboxes_res']['labels_3d']):
                if label in chosen_labels:
                    addition_corners.append(self.logui_info['car_bboxes_res']['corners_3d'][idx])
            addition_corners = np.array(addition_corners) * 1000
            if addition_corners.shape[0] > 0:
                for idx in range(addition_corners.shape[0]):
                    box_corners = addition_corners[idx]
                    box_corners_2d = self.pts3dto2d(box_corners)
                    self.show_img = self.draw_box2d(self.img3d, box_corners_2d,[127,0,255],showface=False)


        ## 绘制坐标系
        coodinate_xyz = np.array([[0,0,0],[1,0,0],[0,1,0],[0,0,1]])*300
        coodinate_xyz2d = self.pts3dto2d(coodinate_xyz)
        cv2.arrowedLine(self.img3d, (coodinate_xyz2d[0][0],coodinate_xyz2d[0][1]), (coodinate_xyz2d[3][0],coodinate_xyz2d[3][1]), (255, 0, 0), 3, tipLength=0.05)
        cv2.arrowedLine(self.img3d, (coodinate_xyz2d[0][0],coodinate_xyz2d[0][1]), (coodinate_xyz2d[1][0],coodinate_xyz2d[1][1]), (0, 0, 255), 3, tipLength=0.05)
        cv2.arrowedLine(self.img3d, (coodinate_xyz2d[0][0],coodinate_xyz2d[0][1]), (coodinate_xyz2d[2][0],coodinate_xyz2d[2][1]), (0, 255, 0), 3, tipLength=0.05)
        if not self.logui_info['face_bbox']: return self.img3d
        if self.logui_info['boxRes']['valid']:
            print(self.logui_info['boxRes']['type'])
            if self.logui_info['boxRes']['type'] == 'Nearest':
                nearest_line = self.pts3dto2d(self.logui_info['boxRes']['line_corner']+[self.logui_info['boxRes']['interactioin_on_ray_seg'][1].tolist()])
                cv2.line(self.img3d, (nearest_line[0,0],nearest_line[0,1]), (nearest_line[1,0],nearest_line[1,1]), (0,0,255), thickness=2)
                cv2.circle(self.img3d,(nearest_line[2,0],nearest_line[2,1]),radius=5,color=(255,255,0),thickness=-1) 

            if self.logui_info['boxRes']['type'] == 'Cross':
                facecorner = self.pts3dto2d(self.logui_info['boxRes']['surface_corner'])
                rate = 0.5
                vertices = facecorner.astype('int32').reshape(1,-1,2)
                poly = copy.deepcopy(self.img3d)
                cv2.fillPoly(poly, vertices, (0,0,255))
                self.img3d = np.clip(self.img3d*(1-rate) + poly*rate,0,255).astype('uint8')
                cross2d = self.pts3dto2d(np.array(self.logui_info['boxRes']['interactioin_in_surface']).reshape(-1,3))
                cv2.circle(self.img3d,cross2d[0].astype('int'),radius=5,color=(255,255,0), thickness=-1)
        # pdb.set_trace()
        if self.logui_info.get('anchor', None) is not None:
            anchor = np.concatenate([self.logui_info['anchor'],np.ones((self.logui_info['anchor'].shape[0],1))],axis=-1)
            anchor4d = (self.in2outMatrix @ anchor.T).T
            anchor = anchor4d[...,:3] / anchor4d[...,3:]
            anchor2d = self.pts3dto2d(anchor)
            for idx in range(anchor2d.shape[0]):
                cv2.circle(self.img3d,anchor2d[idx],radius=3,color=(0,255,0), thickness=-1)
                cv2.putText(self.img3d, str(idx+1), (anchor2d[idx][0], anchor2d[idx][1]-30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,0), 2, 1)

        rate = 300
        dms_coodinate_xyz = np.array([[0,0,0,1],[rate*1,0,0,1],[0,rate*1,0,1],[0,0,rate*1,1]])
        dms_coodinate_xyz4d = (self.in2outMatrix @ dms_coodinate_xyz.T).T
        dms_coodinate_xyz = dms_coodinate_xyz4d[...,:3] / dms_coodinate_xyz4d[...,3:]
        dms_coodinate_xyz2d = self.pts3dto2d(dms_coodinate_xyz)
        cv2.arrowedLine(self.img3d, (dms_coodinate_xyz2d[0][0],dms_coodinate_xyz2d[0][1]), (dms_coodinate_xyz2d[3][0],dms_coodinate_xyz2d[3][1]), (255, 0, 0), 3, tipLength=0.05)
        cv2.arrowedLine(self.img3d, (dms_coodinate_xyz2d[0][0],dms_coodinate_xyz2d[0][1]), (dms_coodinate_xyz2d[1][0],dms_coodinate_xyz2d[1][1]), (0, 0, 255), 3, tipLength=0.05)
        cv2.arrowedLine(self.img3d, (dms_coodinate_xyz2d[0][0],dms_coodinate_xyz2d[0][1]), (dms_coodinate_xyz2d[2][0],dms_coodinate_xyz2d[2][1]), (0, 255, 0), 3, tipLength=0.05)
        # self.draw_ray3dto2d(self.logui_info['RayRes']['gaze_origin'],self.logui_info['RayRes']['gaze_vec'],max(np.linalg.norm(self.logui_info['Boxes'],axis=-1).reshape(-1)),[1,165/255.,0])
        # pdb.set_trace()
        self.draw_ray3dto2d(self.logui_info['RayRes']['gaze_origin'],self.logui_info['RayRes']['gaze_vec'],color=[1,165/255.,0])
        # cv2.imwrite('img.png',self.img3d)

        return self.img3d

    def show3d(self, logui_info, geoms = [], show=False):
        self.logui_info = logui_info
        coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1000)
        show_boxes = [coordinate_frame]
        show_boxes.append(self.getGround(self.logui_info['Boxes'].reshape(-1,3),self.logui_info['RayRes']['gaze_origin']))
        if show:
            color1 = [0,1,0.5]
            color2 = [1,0,0.5]
        else:
            color1 = [0,1,0]
            color2 = [0,0,0.5]
        # pdb.set_trace()
        for idx in range(self.logui_info['Boxes'].shape[0]):
            # pdb.set_trace()
            box_corners = self.logui_info['Boxes'][idx]
            if self.logui_info['boxRes']["valid"]:
                if idx == self.logui_info['boxRes']['box_idx']:
                    show_boxes.extend(self.draw_box(box_corners,color1))
                else:
                    show_boxes.extend(self.draw_box(box_corners,color2))
            else:
                show_boxes.extend(self.draw_box(box_corners))
        geoms += show_boxes
        show_ray = self.draw_ray(self.logui_info['RayRes']['gaze_origin'],self.logui_info['RayRes']['gaze_vec'],max(self.logui_info['Boxes'][...,-1].reshape(-1)),[1,165/255.,0])
        geoms += show_ray
        # pdb.set_trace()
        if self.logui_info['boxRes']["valid"]:
            print(self.logui_info['boxRes']['type'])
            if self.logui_info['boxRes']['type'] == 'Nearest':
                nearest_line = o3d.geometry.LineSet()
                nearest_line.points = o3d.utility.Vector3dVector(np.array(self.logui_info['boxRes']['line_corner']))
                nearest_line.lines = o3d.utility.Vector2iVector([[0,1]])
                nearest_line.colors = o3d.utility.Vector3dVector([[1,0,0]])
                geoms.append(nearest_line)

                nearest_pt = o3d.geometry.TriangleMesh.create_sphere(radius=50)
                nearest_pt.compute_vertex_normals()
                nearest_pt.paint_uniform_color([1,1,0])
                nearest_pt.translate(self.logui_info['boxRes']['interactioin_on_ray_seg'][1].tolist())
                geoms.append(nearest_pt)

            if self.logui_info['boxRes']['type'] == 'Cross':
                mesh = self.create_and_visualize_transparent_surface(self.logui_info['boxRes']['surface_corner'],color=[1,0,0])
                geoms.append(mesh)

                cross_pt = o3d.geometry.TriangleMesh.create_sphere(radius=50)
                cross_pt.compute_vertex_normals()
                cross_pt.paint_uniform_color([1,1,0])
                cross_pt.translate(self.logui_info['boxRes']['interactioin_in_surface'])
                geoms.append(cross_pt)
        if show:
            o3d.visualization.draw_geometries(geoms)
        # pdb.set_trace()
        return geoms
        # return self.save3dfig(geoms)

    def show2d(self, outframe):
        # pdb.set_trace()
        # self.show_img = cv2.undistort(outframe, self.outK, self.outdist)
        self.show_img = copy.deepcopy(outframe)
        bboxes2d = pt3dto2d(self.logui_info['Boxes'].reshape(-1,3), self.outK, self.outdist).reshape(-1,8,2)
        # bboxes2d = pt3dto2d(self.logui_info['Boxes'].reshape(-1,3), self.outK, np.array([0,0,0,0,0.0])).reshape(-1,8,2)
        # gazeorg2d = pt3dto2d(self.logui_info['RayRes']['gaze_origin'].reshape(-1,3), self.outK).reshape(2).astype('int')
        # gazevec2d = pt3dto2d((self.logui_info['RayRes']['gaze_origin']+self.logui_info['RayRes']['gaze_vec']).reshape(-1,3), self.outK).reshape(2)
        bboxes2d_valid = np.all(self.logui_info['Boxes'][...,2]>0,axis=-1)
        for idx in range(bboxes2d.shape[0]):
            box_corners = bboxes2d[idx]
            if not bboxes2d_valid[idx]: continue
            # pdb.set_trace()
            # if self.logui_info['face_bbox'] and self.logui_info['boxRes']['valid']:
            #     if idx == self.logui_info['boxRes']['box_idx']:
            #         self.show_img = self.draw_box2d(self.show_img, box_corners,[0,255,0])
            #     else:
            #         self.show_img = self.draw_box2d(self.show_img, box_corners)
            # else:
            #     self.show_img = self.draw_box2d(self.show_img, box_corners)
            self.show_img = self.draw_box2d(self.show_img, box_corners)
            headbottomcenter = np.mean(self.logui_info['Boxes'][idx][[6,7]],axis=0)
            headdist = np.linalg.norm(headbottomcenter)/1000
            cv2.putText(self.show_img, '{}_{:.1f},{:.1f},{:.1f}'.format(idx,headdist,headbottomcenter[0]/1000,headbottomcenter[2]/1000), (box_corners[6][0], box_corners[6][1]+30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,0), 2, 1)
            taildist = np.linalg.norm(np.mean(self.logui_info['Boxes'][idx][[0,1]],axis=0))/1000
            cv2.putText(self.show_img, '{:.1f}'.format(taildist), (box_corners[0][0], box_corners[0][1]-30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,0), 2, 1)
        if 'addition_show_label' in self.logui_config:
            chosen_labels = [self.logui_config["boxes_label"][i] for i in self.logui_config["addition_show_label"]]
            addition_corners = []
            for idx,label in enumerate(self.logui_info['car_bboxes_res']['labels_3d']):
                if label in chosen_labels:
                    addition_corners.append(self.logui_info['car_bboxes_res']['corners_3d'][idx])
            addition_corners = np.array(addition_corners) * 1000
            # pdb.set_trace()
            if addition_corners.shape[0] > 0: 
                addition_bboxes2d = pt3dto2d(addition_corners.reshape(-1,3), self.outK, self.outdist).reshape(-1,8,2)
                for idx in range(addition_bboxes2d.shape[0]):
                    box_corners = addition_bboxes2d[idx]
                    self.show_img = self.draw_box2d(self.show_img, box_corners,[127,0,255],showface=False)
                    cv2.putText(self.show_img, '{}'.format(idx), (box_corners[6][0], box_corners[6][1]+30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (127,0,255), 2, 1)

        # pdb.set_trace()
        # if not self.logui_info['face_bbox'] or not self.logui_info['boxRes']['valid']: return self.show_img
        if self.logui_info['boxRes']['type'] == 'Nearest':
            linevalid = np.all(np.array(self.logui_info['boxRes']['line_corner']).reshape(-1,3)[:,2]>0,axis=-1)
            if linevalid:
                linecorner = pt3dto2d(np.array(self.logui_info['boxRes']['line_corner']).reshape(-1,3), self.outK, self.outdist).reshape(-1,2)
                cv2.line(self.show_img, linecorner[0].astype('int'), 
                     linecorner[1].astype('int'), (0,0,255), thickness=2)
                nearest2d = pt3dto2d(self.logui_info['boxRes']['interactioin_on_ray_seg'][1].reshape(-1,3), self.outK, self.outdist).reshape(-1,2)
                cv2.circle(self.show_img,nearest2d[0].astype('int'),radius=5,color=(0,255,255), thickness=-1)

        if self.logui_info['boxRes']['type'] == 'Cross':
            facevalid = np.all(np.array(self.logui_info['boxRes']['surface_corner']).reshape(-1,3)[:,2]>0,axis=-1)
            if facevalid:
                facecorner = pt3dto2d(np.array(self.logui_info['boxRes']['surface_corner']).reshape(-1,3), self.outK, self.outdist).reshape(-1,4,2)
                rate = 0.5
                vertices = facecorner.astype('int32')
                poly = copy.deepcopy(self.show_img)
                cv2.fillPoly(poly, vertices, (0,0,255))
                print('img shape00:', self.show_img.shape)
                cv2.imwrite("tt1.png", self.show_img)
                self.show_img = np.clip(self.show_img*(1-rate) + poly*rate,0,255).astype('uint8')
                print('img shape22:', self.show_img.shape)
                print('rate:', rate)

                cross2d = pt3dto2d(np.array(self.logui_info['boxRes']['interactioin_in_surface']).reshape(-1,3), self.outK, self.outdist).reshape(-1,2)
                # print("data:",np.array(self.logui_info['boxRes']['interactioin_in_surface']).reshape(-1,3))
                # print("elf.outK:", self.outK)
                # print("outdist:", self.outdist)
                print("cross2d:", cross2d)
                # print("img_w:", self.show_img.shape[1]," img_h:", self.show_img.shape[0])

                cv2.circle(self.show_img,cross2d[0].astype('int'),radius=5,color=(0,255,0), thickness=-1)
                cv2.imwrite("tt2.png", self.show_img)
        # cv2.imwrite(self.save_path,self.show_img)
        return self.show_img

    def draw_box2d(self, img, box, color = [255,0,0], rate = 0.5, showface=True):
        lines = [
            [4,5],#前上 
            [7,6],#前下 
            [4,7],#前左 
            [5,6],#前右 
            [0,1],#后上 
            [3,2],#后下 
            [0,3],#后左 
            [1,2],#后右 
            [4,0],
            [5,1],
            [6,2],
            [7,3],
        ]
        # for line in lines:
        for line in lines:
            cv2.line(img, box[line[0]].astype('int'), box[line[1]].astype('int'), color, thickness=2)
        if showface:
            vertices = np.array([box[4],box[5],box[6],box[7]]).astype('int32').reshape(-1,4,2)
            poly = copy.deepcopy(img)
            # pdb.set_trace()
            cv2.fillPoly(poly, vertices, color)
            img = np.clip(img*(1-rate) + poly*rate,0,255).astype('uint8')

        return img
    
    def getGround3dto2d_VanishY(self):
        '''
        cos(pitch)*y + sin(pitch)*z -h = 0
        '''
        
        def VanishY2Pitch(vy, K):
            fy, cy = K[1][1], K[1][2]
            pitch = -np.arctan2(vy - cy, fy)
            return pitch
        pitch = VanishY2Pitch(self.cam_matrix['OutcarCamera']["VanishY"],self.outK)
        h = self.cam_matrix['OutcarCamera']["CameraHeight"] * 1000
        self.ground_param = [0, -np.sin(pitch)/np.cos(pitch), h/np.cos(pitch)]
        xz_y = np.array([[10000,0,60000],[10000,0,-650],[-10000,0,-650],[-10000,0,60000]])
        xz_y[:,1] = self.ground_param[0] * xz_y[:,0] + self.ground_param[1] * xz_y[:,2] + self.ground_param[2]
        
        vertices = self.pts3dto2d(xz_y).astype('int32').reshape(1,-1,2)
        cv2.fillPoly(self.img3d, vertices, (100,100,100))
    
    def getGround3dto2d(self, pts, ori):
        # pdb.set_trace()
        bottom_corners = pts.reshape(-1,8,3)[:,[2,3,6,7]]
        # pdb.set_trace()
        self.ground_param = fit_plane(bottom_corners.reshape(-1,3))
        grd_y = np.min(pts.reshape(-1,8,3)[:,[2,3,6,7],1])
        # n,d = fit_plane_least_squares(grdpts)
        max_xyz = np.max(np.vstack((pts,ori)),axis=0)
        max_x = np.max(np.abs(np.vstack((pts,ori))),axis=0)
        min_xyz = np.min(np.vstack((pts,ori)),axis=0)
        # pdb.set_trace()
        # print(max_x[0],max_xyz[2],min_xyz[2])
        # xz_y = np.array([[max_x[0],grd_y,max_xyz[2]],[max_x[0],grd_y,min_xyz[2]],[-max_x[0],grd_y,min_xyz[2]],[-max_x[0],grd_y,max_xyz[2]]])
        xz_y = np.array([[10000,grd_y,60000],[10000,grd_y,-650],[-10000,grd_y,-650],[-10000,grd_y,60000]])
        xz_y[:,1] = self.ground_param[0] * xz_y[:,0] + self.ground_param[1] * xz_y[:,2] + self.ground_param[2]
        # xz_y[:,1] = (-d-n[0]*xz_y[:,0]-n[2]*xz_y[:,2])/n[1]
        
        vertices = self.pts3dto2d(xz_y).astype('int32').reshape(1,-1,2)
        cv2.fillPoly(self.img3d, vertices, (100,100,100))

    def getGround(self, pts, ori):
        # pdb.set_trace()
        bottom_corners = pts.reshape(-1,8,3)[:,[2,3,6,7]]
        self.ground_param = fit_plane(bottom_corners.reshape(-1,3))
        grd_y = np.min(pts.reshape(-1,8,3)[:,[2,3,6,7],1])
        # n,d = fit_plane_least_squares(grdpts)
        max_xyz = np.max(np.vstack((pts,ori)),axis=0)+2000
        min_xyz = np.min(np.vstack((pts,ori)),axis=0)-2000
        xz_y = np.array([[max_xyz[0],grd_y,max_xyz[2]],[max_xyz[0],grd_y,min_xyz[2]],[min_xyz[0],grd_y,min_xyz[2]],[min_xyz[0],grd_y,max_xyz[2]]])
        xz_y[:,1] = self.ground_param[0] * xz_y[:,0] + self.ground_param[1] * xz_y[:,1] + self.ground_param[2]
        # xz_y[:,1] = (-d-n[0]*xz_y[:,0]-n[2]*xz_y[:,2])/n[1]
        triangles = np.array([
            [0, 2, 1],
            [0, 3, 2],
            [0, 1, 2],
            [0, 2, 3]
        ])
        # 创建 TriangleMesh 对象
        mesh = o3d.geometry.TriangleMesh()
        mesh.vertices = o3d.utility.Vector3dVector(xz_y)
        mesh.triangles = o3d.utility.Vector3iVector(triangles)
        # 计算顶点法线
        mesh.compute_vertex_normals()
        # 设置面的颜色
        mesh.paint_uniform_color([0.5,0.5,0.5])  
        # pdb.set_trace()
        return mesh

    def draw_box(self, box, color = [0,0,0.5]):
        # box = box_yz
        # box[:,1] = -box_yz[:,1]
        # box[:,0] = -box_yz[:,0]
        lines = [
            [4,5],#前上 
            [7,6],#前下 
            [4,7],#前左 
            [5,6],#前右 
            [0,1],#后上 
            [3,2],#后下 
            [0,3],#后左 
            [1,2],#后右 
            [4,0],
            [5,1],
            [6,2],
            [7,3],
        ]
        # for line in lines:
        # pdb.set_trace()
        pts_array = np.array(box)
        colors = np.array([color]*12)
        # lines_array = np.array(lines)
        line_set = o3d.geometry.LineSet()
        line_set.points = o3d.utility.Vector3dVector(pts_array)
        line_set.lines = o3d.utility.Vector2iVector(lines)
        line_set.colors = o3d.utility.Vector3dVector(colors)
        vertices = np.array([box[4],box[5],box[6],box[7]])
        mesh = self.create_and_visualize_transparent_surface(vertices,color=color)
        # self.vis.add_geometry(mesh)
        # self.show()
        # pdb.set_trace()
        # self.vis.add_geometry(line_set)
        return [line_set,mesh]

    def draw_ray3dto2d(self, origin, direction, length=1000000, color=[1,165/255.,0]):
        direction = direction / np.linalg.norm(direction)
        # dir_z = direction[-1] if direction[-1] != 0 else 1e-7
        # end_point = origin + direction * abs(length) / dir_z
        o1,o2,o3 = origin 
        v1,v2,v3 = direction 
        a,b,c = self.ground_param
        t = (o2-a*o1-b*o3-c)/(a*v1+b*v3-v2)
        if t > 0:
            end_point = origin + direction * t
        else:
            end_point = origin + direction * abs(length)
        pts2d = self.pts3dto2d([origin,end_point])
        # pdb.set_trace()
        if t > 0:
            cv2.circle(self.img3d,(pts2d[1,0],pts2d[1,1]),10,(0,127,255),-1) 
        cv2.circle(self.img3d,(pts2d[0,0],pts2d[0,1]),10,(0,255,255),-1) 
        cv2.line(self.img3d, (pts2d[0,0],pts2d[0,1]), (pts2d[1,0],pts2d[1,1]), (0,255,255), thickness=2)

    def draw_ray(self, origin, direction, length, color):

        # pdb.set_trace()
        direction = direction / np.linalg.norm(direction)
        dir_z = direction[-1] if direction[-1] != 0 else 1e-7
        end_point = origin + direction * abs(length) / dir_z

        line_set = o3d.geometry.LineSet()
        line_set.points = o3d.utility.Vector3dVector(np.vstack([origin, end_point]))
        line_set.lines = o3d.utility.Vector2iVector([[0,1]])
        line_set.colors = o3d.utility.Vector3dVector([color])

        sphere = o3d.geometry.TriangleMesh.create_sphere(radius=50)
        # 计算顶点法线，使球在可视化时有更好的光照效果
        sphere.compute_vertex_normals()
        # 设置球的颜色
        sphere.paint_uniform_color(color)
        # 将球平移到指定位置
        sphere.translate(origin.tolist())

        return [line_set,sphere]
    
    def create_and_visualize_transparent_surface(self,vertices,color=[1, 1, 0]):
        """
        创建并可视化一个半透明的面
        :return: 无
        """
        # 定义面的三角形面片
        triangles = np.array([
            [0, 2, 1],
            [0, 3, 2],
            [0, 1, 2],
            [0, 2, 3]
        ])
        # 创建 TriangleMesh 对象
        mesh = o3d.geometry.TriangleMesh()
        mesh.vertices = o3d.utility.Vector3dVector(vertices)
        mesh.triangles = o3d.utility.Vector3iVector(triangles)
        # 计算顶点法线
        mesh.compute_vertex_normals()

        # 设置透明度
        # mesh.paint_uniform_color([1, 0, 0])  # 确保颜色设置正确
        # mesh.compute_vertex_normals()  # 重新计算法线
        # mesh.material.set_transparency(0.5)  # 设置透明度为 0.5

        # 设置面的颜色
        mesh.paint_uniform_color(color)  # 红色
        # 设置透明度（使用 RGBA 颜色）
        # alpha = 0.5  # 透明度
        # color = np.array([1, 0, 0, alpha])  # 红色，透明度为 0.5
        # pdb.set_trace()
        # mesh.vertex_colors = o3d.utility.Vector3dVector(np.tile(color, (len(vertices), 1)))
        return mesh
    
    def save3dfig(self, geoms):
        # pdb.set_trace()
        vis = o3d.visualization.Visualizer()
        vis.create_window(visible=False)

        # 添加点云到窗口
        for item in geoms:
            vis.add_geometry(item)
        # vis.add_geometry(pcd)
        # vis.add_geometry(sphere)

        # 获取视角控制器
        view_ctl = vis.get_view_control()

        # 设置特定视角 (例如，俯视角度)
        # 定义lookat, front, up, 和 zoom 参数
        lookat = [0.5, 0.5, 2000]  # 中心点
        front = [0, -0.8, -1]  # 观察点方向
        up = [0, -1, 0]  # 向上的方向
        zoom = 0.3  # 缩放级别

        # 更新视角
        view_ctl.set_lookat(lookat)
        view_ctl.set_front(front)
        view_ctl.set_up(up)
        view_ctl.set_zoom(zoom)

        # 保存当前视图为 .png 文件
        vis.poll_events()
        vis.update_renderer()
        # pdb.set_trace()
        vis.capture_screen_image(".output.png")

        # 关闭窗口
        vis.destroy_window()

        return cv2.imread(".output.png")
        
    def Wholebody_logui(self, wholebody):
        bbox = wholebody["face_bbox"]
        cv2.rectangle(self.inframe, (int(bbox[0]), int(bbox[1])), (int(bbox[2]), int(bbox[3])), (0, 0, 255), 2)

    def headpose(self, headpose):
        if 'pose_vrf' in headpose:
            T = np.array([[  0,  0,  0, 0],
                        [ 0,  0,  0, 0],
                        [  0,   0,  0.0, 953]])

            if 'pose_undist' in headpose:
                K = copy.deepcopy(headpose['K'] )
                gray_logui = obj_draw(self.inframe, headpose['pose_undist'], 600, 100, T, K)
                self.inframe = cv2.cvtColor(gray_logui, cv2.COLOR_GRAY2BGR)
            else:
                K = copy.deepcopy(headpose['K'] )
                gray_logui = obj_draw(self.inframe, headpose['pose_out'], 600, 100, T, K)
                self.inframe = cv2.cvtColor(gray_logui, cv2.COLOR_GRAY2BGR)                

            headpose_log = ["{:.2f},".format(num) for num in headpose['pose_out']]
            headpose_log = ' '.join(headpose_log)
            self.draw_text("pose_out: " + headpose_log) 

            if 'pose_undist' in headpose:
                headpose_log = ["{:.2f},".format(num) for num in headpose['pose_undist']]
                headpose_log = ' '.join(headpose_log)
                self.draw_text("headpose undist: " + headpose_log)  

            headpose_log = ["{:.2f},".format(num) for num in headpose['pose_vrf']]
            headpose_log = ' '.join(headpose_log)
            self.draw_text("headpose vrf: " + headpose_log)                     
            
        if 'lmks_raw' in headpose:
            hp = np.array(headpose['lmks_raw']).reshape(-1,2)
            for idx in range(hp.shape[0]):
                if headpose['occ_raw'][idx][0] < headpose['occ_raw'][idx][1]:
                    cv2.circle(self.inframe,(int(hp[idx,0]),int(hp[idx,1])),2,(0,255,0),-1)  
                else:
                    cv2.circle(self.inframe,(int(hp[idx,0]),int(hp[idx,1])),2,(0,255,0),-1)  
            
            self.draw_text("align_cls: " + str(headpose['align_cls']))    

    def head3d(self, head3d):
        self.draw_text('left_eye3d:{:.2f},{:.2f},{:.2f}'.format(head3d['left_eye3d'][0],head3d['left_eye3d'][1],head3d['left_eye3d'][2]))
        self.draw_text('right_eye3d:{:.2f},{:.2f},{:.2f}'.format(head3d['right_eye3d'][0],head3d['right_eye3d'][1],head3d['right_eye3d'][2]))
    
    def gaze_logui(self, gaze_logui_info):
        # pdb.set_trace()
        if 'logui_text_gazevec' in gaze_logui_info:
            for casetxt in gaze_logui_info['logui_text_gazevec']:
                self.draw_text(casetxt)
        if 'logui_gazedir' in gaze_logui_info:   
            self.draw_line(gaze_logui_info['logui_gazedir']['left_eye_gazedir'])
            self.draw_line(gaze_logui_info['logui_gazedir']['right_eye_gazedir'])
            self.draw_line(gaze_logui_info['logui_gazedir']['center_eye_gazedir'])
        if 'logui_cropeye' in gaze_logui_info:
            lefteye = np.tile(gaze_logui_info['logui_cropeye']['lefteye'][:,:,np.newaxis],(1,1,3))
            righteye = np.tile(gaze_logui_info['logui_cropeye']['righteye'][:,:,np.newaxis],(1,1,3))
            sh,sw = lefteye.shape[:2]
            self.inframe[-2*sh-5:-sh-5,-2*sw-5:-sw-5] = lefteye
            self.inframe[-sh:,-2*sw-5:-sw-5] = lefteye
            self.inframe[-2*sh-5:-sh-5,-sw:] = righteye
            self.inframe[-sh:,-sw:] = righteye
        if 'logui_raw_gaze_res' in gaze_logui_info:
            frame_height, frame_width, _ = self.inframe.shape
            left_ori = np.array([frame_width+(-2*sw-5+-sw-5)/2,frame_height+(-2*sh-5+-sh-5)/2])
            right_ori = np.array([frame_width+(-sw)/2,frame_height+(-2*sh-5+-sh-5)/2])
            # pdb.set_trace()
            self.draw_line([left_ori,left_ori+np.array([-gaze_logui_info['logui_raw_gaze_res']['left_gaze_point'][0]/2,
                                                        -gaze_logui_info['logui_raw_gaze_res']['left_gaze_point'][1]/2]),
                                                        gaze_logui_info['logui_raw_gaze_res']['left_valid']])
            self.draw_line([right_ori,right_ori+np.array([-gaze_logui_info['logui_raw_gaze_res']['right_gaze_point'][0]/2,
                                                          -gaze_logui_info['logui_raw_gaze_res']['right_gaze_point'][1]/2]),
                                                          gaze_logui_info['logui_raw_gaze_res']['right_valid']])
    
    def draw_line(self, pts):
        pt1 = pts[0].astype('int')
        pt2 = pts[1].astype('int')
        if not isinstance(pts[2],int):
            green = int(255*pts[2])
            color = (0,green,255)
            # pdb.set_trace()
        else:
            color = (0,255,0) if pts[2] == 1 else (0,0,255)
        cv2.circle(self.inframe,(pt1[0],pt1[1]),2,(0,255,255),-1)
        cv2.line(self.inframe, (pt1[0],pt1[1]), (pt2[0],pt2[1]), color, thickness=2) 

# if __name__ == '__main__':
