#!/usr/bin/env python3
"""
简化的流式接口测试程序 - 专注于展示逐字打印效果
"""

import requests
import json
import time
import os

# 测试配置
API_URL = "http://localhost:8000/recognize"
API_KEY = "sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"
TEST_IMAGE_PATH = "dvr_520.png"

def test_streaming_only():
    """只测试流式接口，展示逐字打印效果"""
    print("=== 流式接口逐字打印测试 ===")
    
    # 确保测试图片存在
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"测试图片不存在: {TEST_IMAGE_PATH}")
        return
    
    # 准备请求数据
    files = {
        'outcar_image': ('test.png', open(TEST_IMAGE_PATH, 'rb'), 'image/png')
    }
    
    data = {
        'mix_gaze': json.dumps([0.37740352, -0.24775083, -0.89148018]),
        'gaze_origin': json.dumps([-29.56759495, -102.30740821, 520.42972422]),
        'gaze_valid': 3,
        'prompt': "用300字描述这张图片中的内容",
        'call_jarvis': True,
        'stream': True
    }
    
    headers = {
        'apl-key': API_KEY,
        'Accept': 'text/event-stream'
    }
    
    try:
        start_time = time.time()
        response = requests.post(API_URL, files=files, data=data, headers=headers, stream=True)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            chunk_count = 0
            first_chunk = True
            accumulated_output = ""
            
            print("开始接收流式数据...\n")
            
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith("data: "):
                    try:
                        data_str = line[6:]  # 去掉 "data: " 前缀
                        data_obj = json.loads(data_str)
                        chunk_count += 1
                        
                        if first_chunk:
                            # 第一次显示基本信息
                            print("🚗 车辆识别信息:")
                            print(f"   车辆ID: {data_obj.get('itemID', 'N/A')}")
                            print(f"   检测框: {data_obj.get('2DRect_ori', 'N/A')}")
                            print(f"   Gaze落点: {data_obj.get('gaze_point', 'N/A')}")
                            print(f"   Jarvis ID: {data_obj.get('jarvis_id', 'N/A')}")
                            print("\n📝 AI描述内容:")
                            print("   ", end='', flush=True)
                            first_chunk = False
                        
                        # 获取当前输出内容
                        current_output = data_obj.get('output', '')
                        
                        # 检查是否是最终完整结果
                        if 'stream_state' not in data_obj:
                            # 最终结果
                            print(f"\n\n✅ 描述完成!")
                            print(f"📊 统计信息:")
                            print(f"   总时间: {time.time() - start_time:.2f}秒")
                            print(f"   数据块: {chunk_count}个")
                            print(f"   内容长度: {len(current_output)}字符")
                            break
                        else:
                            # 流式输出，逐字显示
                            for char in current_output:
                                print(char, end='', flush=True)
                                time.sleep(0.02)  # 20ms延迟，更快的打字机效果
                            accumulated_output += current_output
                            
                    except json.JSONDecodeError as e:
                        print(f"\n❌ JSON解析失败: {e}")
        else:
            print(f"❌ 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    finally:
        files['outcar_image'][1].close()

if __name__ == "__main__":
    print("🚀 启动流式接口逐字打印测试...")
    test_streaming_only()
    print("\n🎉 测试完成!")
