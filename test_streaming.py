#!/usr/bin/env python3
"""
测试流式接口的脚本
"""

import requests
import json
import time
import os

# 测试配置
API_URL = "http://localhost:8000/recognize"
API_KEY = "sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"

# 测试图片路径 - 使用真实的车载图片
TEST_IMAGE_PATH = "dvr_520.png"

def test_non_streaming():
    """测试非流式接口"""
    print("=== 测试非流式接口 ===")

    # 确保测试图片存在
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"测试图片不存在: {TEST_IMAGE_PATH}")
        return
    
    # 准备请求数据
    files = {
        'outcar_image': ('test.png', open(TEST_IMAGE_PATH, 'rb'), 'image/png')
    }
    
    data = {
        'mix_gaze': json.dumps([0.37740352, -0.24775083, -0.89148018]),
        'gaze_origin': json.dumps([-29.56759495, -102.30740821, 520.42972422]),
        'gaze_valid': 3,
        'prompt': "用300zi请描述这张图片中的内容",
        'call_jarvis': True,
        'stream': False
    }
    
    headers = {
        'apl-key': API_KEY
    }
    
    try:
        start_time = time.time()
        response = requests.post(API_URL, files=files, data=data, headers=headers)
        end_time = time.time()
        
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {e}")
    finally:
        files['outcar_image'][1].close()

def test_streaming():
    """测试流式接口"""
    print("\n=== 测试流式接口 ===")

    # 确保测试图片存在
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"测试图片不存在: {TEST_IMAGE_PATH}")
        return
    
    # 准备请求数据
    files = {
        'outcar_image': ('test.png', open(TEST_IMAGE_PATH, 'rb'), 'image/png')
    }
    
    data = {
        'mix_gaze': json.dumps([0.37740352, -0.24775083, -0.89148018]),
        'gaze_origin': json.dumps([-29.56759495, -102.30740821, 520.42972422]),
        'gaze_valid': 3,
        'prompt': "请描述这张图片中的内容",
        'call_jarvis': True,
        'stream': True
    }
    
    headers = {
        'apl-key': API_KEY,
        'Accept': 'text/event-stream'
    }
    
    try:
        start_time = time.time()
        response = requests.post(API_URL, files=files, data=data, headers=headers, stream=True)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("开始接收流式数据:")
            chunk_count = 0
            first_chunk = True
            accumulated_output = ""

            for line in response.iter_lines(decode_unicode=True):
                if line:
                    chunk_count += 1

                    # 解析数据
                    if line.startswith("data: "):
                        try:
                            data_str = line[6:]  # 去掉 "data: " 前缀
                            data_obj = json.loads(data_str)

                            if first_chunk:
                                # 第一次打印完整信息（除了output）
                                print("=" * 50)
                                print("流式响应信息:")
                                print(f"状态码: {data_obj.get('code', 'N/A')}")
                                print(f"车辆ID: {data_obj.get('itemID', 'N/A')}")
                                print(f"检测框: {data_obj.get('2DRect_ori', 'N/A')}")
                                print(f"Gaze落点: {data_obj.get('gaze_point', 'N/A')}")
                                print(f"Jarvis ID: {data_obj.get('jarvis_id', 'N/A')}")
                                print("=" * 50)
                                print("实时输出内容: ", end='', flush=True)
                                first_chunk = False

                            # 获取当前输出内容
                            current_output = data_obj.get('output', '')

                            # 检查是否是最终完整结果
                            if 'stream_state' not in data_obj:
                                # 最终结果，显示完整内容并结束
                                print(f"\n\n{'='*50}")
                                print(f"最终完整结果:")
                                print(f"'{current_output}'")
                                print(f"{'='*50}")
                            else:
                                # 流式输出，每个chunk都是新的片段，直接显示
                                print(current_output, end='', flush=True)
                                accumulated_output += current_output

                        except json.JSONDecodeError as e:
                            print(f"\nJSON解析失败: {e}")
                            print(f"原始数据: {line}")

            end_time = time.time()
            print(f"\n\n流式响应完成!")
            print(f"总时间: {end_time - start_time:.2f}秒")
            print(f"总共接收到 {chunk_count} 个数据块")
            print(f"累积输出长度: {len(accumulated_output)} 字符")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"流式请求失败: {e}")
    finally:
        files['outcar_image'][1].close()

def test_health():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"健康检查失败: {e}")

if __name__ == "__main__":
    print("开始测试流式接口...")
    
    # 首先测试健康检查
    test_health()
    
    # 测试非流式接口
    test_non_streaming()
    
    # 测试流式接口
    test_streaming()
    
    print("\n测试完成!")
