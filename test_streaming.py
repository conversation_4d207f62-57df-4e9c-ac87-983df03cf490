#!/usr/bin/env python3
"""
测试流式接口的脚本
"""

import requests
import json
import time
import os

# 测试配置
API_URL = "http://localhost:8000/recognize"
API_KEY = "sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"

# 测试图片路径 - 使用真实的车载图片
TEST_IMAGE_PATH = "dvr_520.png"

def test_non_streaming():
    """测试非流式接口"""
    print("=== 测试非流式接口 ===")

    # 确保测试图片存在
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"测试图片不存在: {TEST_IMAGE_PATH}")
        return
    
    # 准备请求数据
    files = {
        'outcar_image': ('test.png', open(TEST_IMAGE_PATH, 'rb'), 'image/png')
    }
    
    data = {
        'mix_gaze': json.dumps([0.37740352, -0.24775083, -0.89148018]),
        'gaze_origin': json.dumps([-29.56759495, -102.30740821, 520.42972422]),
        'gaze_valid': 3,
        'prompt': "请描述这张图片中的内容",
        'call_jarvis': True,
        'stream': False
    }
    
    headers = {
        'apl-key': API_KEY
    }
    
    try:
        start_time = time.time()
        response = requests.post(API_URL, files=files, data=data, headers=headers)
        end_time = time.time()
        
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {e}")
    finally:
        files['outcar_image'][1].close()

def test_streaming():
    """测试流式接口"""
    print("\n=== 测试流式接口 ===")

    # 确保测试图片存在
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"测试图片不存在: {TEST_IMAGE_PATH}")
        return
    
    # 准备请求数据
    files = {
        'outcar_image': ('test.png', open(TEST_IMAGE_PATH, 'rb'), 'image/png')
    }
    
    data = {
        'mix_gaze': json.dumps([0.37740352, -0.24775083, -0.89148018]),
        'gaze_origin': json.dumps([-29.56759495, -102.30740821, 520.42972422]),
        'gaze_valid': 3,
        'prompt': "请描述这张图片中的内容",
        'call_jarvis': True,
        'stream': True
    }
    
    headers = {
        'apl-key': API_KEY,
        'Accept': 'text/event-stream'
    }
    
    try:
        start_time = time.time()
        response = requests.post(API_URL, files=files, data=data, headers=headers, stream=True)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("开始接收流式数据:")
            chunk_count = 0
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    chunk_count += 1
                    print(f"Chunk {chunk_count}: {line}")
                    
                    # 解析数据
                    if line.startswith("data: "):
                        try:
                            data_str = line[6:]  # 去掉 "data: " 前缀
                            data_obj = json.loads(data_str)
                            print(f"解析数据: {json.dumps(data_obj, ensure_ascii=False, indent=2)}")
                        except json.JSONDecodeError as e:
                            print(f"JSON解析失败: {e}")
                            print(f"原始数据: {line}")
            
            end_time = time.time()
            print(f"流式响应完成，总时间: {end_time - start_time:.2f}秒")
            print(f"总共接收到 {chunk_count} 个数据块")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"流式请求失败: {e}")
    finally:
        files['outcar_image'][1].close()

def test_health():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"健康检查失败: {e}")

if __name__ == "__main__":
    print("开始测试流式接口...")
    
    # 首先测试健康检查
    test_health()
    
    # 测试非流式接口
    test_non_streaming()
    
    # 测试流式接口
    test_streaming()
    
    print("\n测试完成!")
