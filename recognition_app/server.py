import uvicorn
import argparse
import os
import sys
from pathlib import Path

# 获取项目根目录并添加到Python路径中
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))

# 证书文件路径
CERT_FILE = BASE_DIR / "ip.crt"
KEY_FILE = BASE_DIR / "ip.key"

def main():
    parser = argparse.ArgumentParser(description="启动识别服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务监听地址")
    parser.add_argument("--port", type=int, default=8000, help="服务监听端口")
    parser.add_argument("--use-https", action="store_true", help="是否启用HTTPS")
    parser.add_argument("--certfile", default=str(CERT_FILE), help="SSL证书文件路径")
    parser.add_argument("--keyfile", default=str(KEY_FILE), help="SSL私钥文件路径")
    
    args = parser.parse_args()
    
    # 检查证书文件是否存在
    if args.use_https:
        if not os.path.exists(args.certfile):
            raise FileNotFoundError(f"证书文件不存在: {args.certfile}")
        if not os.path.exists(args.keyfile):
            raise FileNotFoundError(f"私钥文件不存在: {args.keyfile}")
        
        print(f"启动HTTPS服务: host={args.host}, port={args.port}")
        print(f"证书文件: {args.certfile}")
        print(f"私钥文件: {args.keyfile}")
        
        # 启用HTTPS
        uvicorn.run(
            "recognition_app.main:app",
            host=args.host,
            port=args.port,
            ssl_certfile=args.certfile,
            ssl_keyfile=args.keyfile
        )
    else:
        print(f"启动HTTP服务: host={args.host}, port={args.port}")
        # 启用HTTP
        uvicorn.run(
            "recognition_app.main:app",
            host=args.host,
            port=args.port
        )

if __name__ == "__main__":
    main()