from fastapi import FastAP<PERSON>, UploadFile, Form, Header, HTTPException
from fastapi.responses import JSONResponse, StreamingResponse
import shutil, os, uuid
import json
import logging
from typing import List, Optional

from recognition_app.recognizer import init_model, recognize_gaze_async, recognize_gaze_streaming, recognize_gaze_memory_streaming

# ==== 日志配置 ====
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

app = FastAPI()
UPLOAD_FOLDER = "recognition_app/temp_images"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# API Key配置
DEFAULT_API_KEY = "sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"
VALID_API_KEY = os.getenv('VALID_API_KEY', DEFAULT_API_KEY)

def verify_api_key(api_key: Optional[str] = Header(None, alias="apl-key")):
    """验证API Key"""
    if not api_key:
        raise HTTPException(
            status_code=401,
            detail={"code": 1001, "output": "Missing API key in header 'apl-key'"}
        )

    if api_key != VALID_API_KEY:
        raise HTTPException(
            status_code=401,
            detail={"code": 1001, "output": "Invalid API key"}
        )

    return api_key

@app.on_event("startup")
def startup_event():
    """服务启动时初始化模型"""
    try:
        init_model()
        logger.info("服务启动完成，模型初始化成功")
    except Exception as e:
        logger.error(f"模型初始化失败: {e}")
        raise

@app.get("/")
def read_root():
    """健康检查端点"""
    return {"message": "Recognition service is running", "status": "ok"}

@app.get("/health")
def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "recognition_app"}

@app.post("/recognize")
async def recognize_endpoint(
    outcar_image: UploadFile,
    mix_gaze: str = Form(..., description="gaze direction vector as JSON array, e.g. [0.37740352, -0.24775083, -0.89148018]"),
    gaze_origin: str = Form(..., description="gaze origin point as JSON array, e.g. [-29.56759495, -102.30740821, 520.42972422]"),
    gaze_valid: int = Form(..., description="gaze validity flag, e.g. 3"),
    prompt: str = Form("", description="prompt for vehicle recognition"),
    call_jarvis: bool = Form(False, description="whether to call JARVIS API for vehicle recognition"),
    stream: bool = Form(False, description="whether to use streaming mode for JARVIS API"),
    api_key: str = Header(..., alias="apl-key", description="API key for authentication")
):
    # 验证API Key
    if not api_key:
        return JSONResponse(
            content={"code": 1001, "output": "Missing API key in header 'apl-key'"},
            status_code=401
        )

    if api_key != VALID_API_KEY:
        return JSONResponse(
            content={"code": 1001, "output": "Invalid API key"},
            status_code=401
        )

    session_id = str(uuid.uuid4())
    outcar_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_outcar.png")

    logger.info(f"开始处理识别请求 - Session ID: {session_id}")
    logger.debug(f"参数: call_jarvis={call_jarvis}, stream={stream}, prompt='{prompt[:50]}...' if len(prompt) > 50 else prompt")

    try:
        # 保存上传的outcar图片
        with open(outcar_path, "wb") as f:
            shutil.copyfileobj(outcar_image.file, f)
        logger.debug(f"图片已保存到: {outcar_path}")

        # 解析gaze信息
        try:
            mix_gaze_array = json.loads(mix_gaze)
            gaze_origin_array = json.loads(gaze_origin)
            logger.debug(f"解析gaze参数成功: mix_gaze={mix_gaze_array}, gaze_origin={gaze_origin_array}, gaze_valid={gaze_valid}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            return JSONResponse(
                content={"code": 1006, "output": f"JSON解析错误: {e}"},
                status_code=400
            )

        # 验证gaze数据格式
        if not (isinstance(mix_gaze_array, list) and len(mix_gaze_array) == 3):
            logger.error("mix_gaze格式错误: 必须是包含3个元素的数组")
            return JSONResponse(
                content={"code": 1006, "output": "mix_gaze必须是包含3个元素的数组"},
                status_code=400
            )

        if not (isinstance(gaze_origin_array, list) and len(gaze_origin_array) == 3):
            logger.error("gaze_origin格式错误: 必须是包含3个元素的数组")
            return JSONResponse(
                content={"code": 1006, "output": "gaze_origin必须是包含3个元素的数组"},
                status_code=400
            )

        # 构建gaze输入数据
        gaze_input = {
            "mix_gaze": mix_gaze_array,
            "gaze_origin": gaze_origin_array,
            "gaze_valid": gaze_valid
        }
        if stream and call_jarvis and prompt and prompt.strip():
            # 流式返回
            logger.info("开始执行流式gaze识别")
            logger.debug(f"流式处理使用文件路径: {outcar_path}")

            # 确保文件已经保存并且可以访问
            if not os.path.exists(outcar_path):
                logger.error(f"流式处理: 文件不存在 {outcar_path}")
                return JSONResponse(
                    content={"code": 1001, "output": None, "error_msg": f"文件不存在: {outcar_path}"},
                    status_code=400
                )

            async def stream_generator():
                """流式响应生成器"""
                try:
                    # 使用流式识别函数
                    for chunk in recognize_gaze_streaming(gaze_input, outcar_path, prompt, call_jarvis):
                        if chunk["type"] == "chunk":
                            # 流式数据块
                            yield f"data: {json.dumps(chunk['data'], ensure_ascii=False)}\n\n"
                        elif chunk["type"] == "final":
                            # 最终结果
                            yield f"data: {json.dumps(chunk['data'], ensure_ascii=False)}\n\n"
                            break
                        elif chunk["type"] == "error":
                            # 错误信息
                            yield f"data: {json.dumps(chunk['data'], ensure_ascii=False)}\n\n"
                            break
                except Exception as e:
                    logger.error(f"流式处理错误: {e}")
                    error_data = {
                        "code": 1999,
                        "output": None,
                        "error_msg": f"流式处理错误: {str(e)}"
                    }
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

            return StreamingResponse(
                stream_generator(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "*"
                }
            )

        # 非流式返回
        logger.info("开始执行gaze识别")
        result = await recognize_gaze_async(gaze_input, outcar_path, prompt, call_jarvis, stream)
        logger.info(f"识别完成 - 结果代码: {result.get('code', 'unknown')}")
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"处理请求时发生错误: {e}")
        return JSONResponse(
            content={"code": 1999, "output": f"服务器内部错误: {str(e)}"},
            status_code=500
        )
    finally:
        # 清理临时文件
        if os.path.exists(outcar_path):
            os.remove(outcar_path)
            logger.debug(f"已清理临时文件: {outcar_path}")
