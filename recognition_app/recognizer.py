import os
import json
import time
import cv2
import requests
import asyncio
import itertools
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import threading
import time
import logging

from modules.build_models import ModelsWarpper

# ==== 日志配置 ====
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# ==== DEBUG配置 ====
DEBUG_SAVE_IMAGES = os.getenv('DEBUG_SAVE_IMAGES', 'False').lower() in ('true', '1', 'yes', 'on')

# ==== 配置路径与参数 ====
PARAM_SETTING_PATH  = "data/configs/param_setting.json"
MODULE_SETTING_PATH = "data/configs/module_setting.json"
CAMERA_PATH         = "data/vehicles/Pst/camera_info_20250707.json"
PROVIDERS           = ["CUDAExecutionProvider"]

# ==== API 请求配置 ====
API_KEY  = "47f18966416f206dd1d49252e13dd2d872feb3bf"
HEADERS  = {
    "Accept": "text/event-stream",
    "Authorization": API_KEY,
    "Client-Info": json.dumps({"sdk_version": "V1.2.3", "tid": "123456"})
}
JARVIS_API = "https://jarvis.senseauto.com:1050/v1/2024_byd_poc"

# ==== 错误码 ====
ERROR_CODES = {
    "SUCCESS":               0,
    "IMAGE_READ_FAIL":     1001,
    "IMAGE_LOAD_FAIL":     1002,
    "CROP_FAIL":           1003,
    "API_REQUEST_FAIL":    1004,
    "RESPONSE_PARSE_FAIL": 1005,
    "FORMAT_ERROR":        1006,
    "NO_GAZE_DETECTED":    1007
}

# ==== 模型实例与线程锁 ====
MODELSWRAPPER       = None
MODELS_INIT_LOCK    = threading.Lock()
MODELS_INITIALIZED  = False
executor            = ThreadPoolExecutor(max_workers=4)

# 全局递增的 request_id 生成器
_request_id_iter = itertools.count()

# ==== JARVIS API响应处理函数 ====
def _process_non_streaming_response(response):
    """处理非流式响应"""
    try:
        raw_text = response.text.strip()
        if not raw_text.startswith("data:"):
            logger.warning("非流式响应格式异常: 不以'data:'开头")
            return None, None

        result_json = json.loads(raw_text[len("data:"):].strip())

        if "results" in result_json and len(result_json["results"]) > 0:
            output = result_json["results"][0].get("output", "")
            jarvis_id = result_json.get("id", "")
            return output, jarvis_id
        else:
            logger.warning("非流式响应格式异常: 缺少results字段")
            return None, None

    except json.JSONDecodeError as e:
        logger.error(f"非流式响应JSON解析失败: {e}")
        return None, None
    except Exception as e:
        logger.error(f"非流式响应处理失败: {e}")
        return None, None

def _process_streaming_response(response, request_start_time):
    """处理流式响应 - 返回生成器用于流式输出"""
    try:
        full_response = ""
        chunk_count = 0
        accumulated_output = ""
        jarvis_id = None

        # 逐块读取流式响应
        for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
            if chunk:
                chunk_count += 1
                full_response += chunk

        request_time = time.time() - request_start_time
        logger.info(f"流式接口请求时间: {request_time:.2f}秒")
        logger.info(f"共接收 {chunk_count} 个数据块")

        # 解析流式响应 - 需要处理多个JSON对象
        data_blocks = [block.strip() for block in full_response.split('\n\n') if block.strip()]

        for i, block in enumerate(data_blocks):
            if block.startswith("data:"):
                try:
                    block_json = json.loads(block[len("data:"):].strip())

                    # 获取jarvis_id
                    if jarvis_id is None:
                        jarvis_id = block_json.get("id", "")

                    if "results" in block_json and len(block_json["results"]) > 0:
                        result = block_json["results"][0]
                        output = result.get("output", "")
                        state = result.get("state", "")

                        # 累积输出内容
                        if output:
                            accumulated_output += output

                        # 如果是最终状态，返回完整结果
                        if state == "stop":
                            return accumulated_output, jarvis_id

                except json.JSONDecodeError as e:
                    logger.error(f"流式响应块 {i+1} JSON解析失败: {e}")
                    continue

        # 如果没有找到stop状态，返回累积的输出
        logger.warning("流式响应未找到最终结果")
        return accumulated_output if accumulated_output else None, jarvis_id

    except Exception as e:
        logger.error(f"流式响应处理失败: {e}")
        return None, None

def _process_streaming_response_generator(response, request_start_time):
    """处理流式响应 - 生成器版本，用于实时流式输出"""
    try:
        accumulated_output = ""
        jarvis_id = None
        chunk_buffer = ""

        logger.info("开始流式处理...")

        # 逐块读取流式响应
        for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
            if chunk:
                chunk_buffer += chunk

                # 尝试解析完整的数据块
                while '\n\n' in chunk_buffer:
                    block, chunk_buffer = chunk_buffer.split('\n\n', 1)
                    block = block.strip()

                    if block.startswith("data:"):
                        try:
                            block_json = json.loads(block[len("data:"):].strip())

                            # 获取jarvis_id
                            if jarvis_id is None:
                                jarvis_id = block_json.get("id", "")

                            if "results" in block_json and len(block_json["results"]) > 0:
                                result = block_json["results"][0]
                                output = result.get("output", "")
                                state = result.get("state", "")

                                if output:
                                    accumulated_output += output
                                    # 实时yield输出片段
                                    yield {
                                        "type": "chunk",
                                        "content": output,
                                        "accumulated": accumulated_output,
                                        "jarvis_id": jarvis_id,
                                        "state": state
                                    }

                                # 如果是最终状态
                                if state == "stop":
                                    yield {
                                        "type": "final",
                                        "content": accumulated_output,
                                        "jarvis_id": jarvis_id,
                                        "state": state
                                    }
                                    return

                        except json.JSONDecodeError as e:
                            logger.error(f"流式响应块JSON解析失败: {e}")
                            continue

        # 处理剩余的缓冲区
        if chunk_buffer.strip():
            block = chunk_buffer.strip()
            if block.startswith("data:"):
                try:
                    block_json = json.loads(block[len("data:"):].strip())
                    if jarvis_id is None:
                        jarvis_id = block_json.get("id", "")
                except:
                    pass

        # 最终结果
        yield {
            "type": "final",
            "content": accumulated_output,
            "jarvis_id": jarvis_id,
            "state": "completed"
        }

    except Exception as e:
        logger.error(f"流式响应生成器处理失败: {e}")
        yield {
            "type": "error",
            "content": str(e),
            "jarvis_id": None,
            "state": "error"
        }

def pt3dto2d(corners, K, dist):
    """将3D坐标投影到2D图像坐标系"""
    point_2d_res, _ = cv2.projectPoints(corners, np.zeros((3,1)), np.zeros((3,1)), K, dist)
    return np.round(point_2d_res).astype('int')

def get_gaze_2d_coordinates(models_result, outK, outdist, camera_config=None):
    """获取gaze落点在outcar_image上的2D坐标

    Args:
        models_result: 模型结果
        outK: 去畸变后的相机内参矩阵
        outdist: 畸变参数 (通常设为0)
        camera_config: 相机配置信息 (可选)

    Returns:
        包含去畸变坐标和原图坐标的字典
    """

    if not models_result.get('boxRes', {}).get('valid', False):
        return None
    box_res = models_result['boxRes']

    if box_res['type'] == 'Cross':
        # Cross类型：gaze与车辆表面的交点
        point_3d = np.array(box_res['interactioin_in_surface']).reshape(-1, 3)

        # 计算去畸变图像坐标 (使用去畸变后的内参矩阵，无畸变参数)
        point_2d_undist = pt3dto2d(point_3d, outK, np.zeros(5)).reshape(-1, 2)
        logger.debug(f"point3d: {point_3d}")
        logger.debug(f"point2d_undist: {point_2d_undist}")

        # 计算原图坐标 (使用原始内参矩阵和畸变参数)
        point_2d_original = point_2d_undist[0].copy()
        if camera_config is not None:
            outcar_config = camera_config['OutcarCamera']
            cam_type = outcar_config.get('Camera_type', 'DMS')
            dist = np.array(outcar_config.get('Distortion', [0.0, 0, 0, 0, 0]))

            if cam_type in ['fisheye', 'OMS']:
                # 鱼眼相机：使用原始内参矩阵
                ori_outK = np.array(outcar_config['CameraIntrinsics_ori'])
                point_2d_original, _ = cv2.fisheye.projectPoints(
                    point_3d[:, np.newaxis, :], np.zeros(3), np.zeros(3), ori_outK, dist[:4]
                )
                point_2d_original = point_2d_original[0, 0]
            else:
                # 针孔相机：使用畸变参数
                point_2d_original, _ = cv2.projectPoints(
                    point_3d, np.zeros(3), np.zeros(3), outK, dist
                )
                point_2d_original = point_2d_original[0, 0]

            logger.debug(f"point2d_original: {point_2d_original}")

        return {
            'type': 'Cross',
            'point_2d_undist': point_2d_undist[0],  # 去畸变图像坐标
            'point_2d_original': point_2d_original,  # 原图坐标
            'point_2d': point_2d_original,  # 保持向后兼容，默认返回原图坐标
            'point_3d': box_res['interactioin_in_surface'],
            'color': (255, 255, 0)  # 黄色
        }

    elif box_res['type'] == 'Nearest':
        # Nearest类型：gaze射线上最近的点
        point_3d = box_res['interactioin_on_ray_seg'][1].reshape(-1, 3)

        # 计算去畸变图像坐标
        point_2d_undist = pt3dto2d(point_3d, outK, np.zeros(5)).reshape(-1, 2)
        logger.debug(f"point3d: {point_3d}")
        logger.debug(f"point2d_undist: {point_2d_undist}")

        # 计算原图坐标
        point_2d_original = point_2d_undist[0].copy()
        if camera_config is not None:
            outcar_config = camera_config['OutcarCamera']
            cam_type = outcar_config.get('Camera_type', 'DMS')
            dist = np.array(outcar_config.get('Distortion', [0.0, 0, 0, 0, 0]))

            if cam_type in ['fisheye', 'OMS']:
                # 鱼眼相机：使用原始内参矩阵
                ori_outK = np.array(outcar_config['CameraIntrinsics_ori'])
                point_2d_original, _ = cv2.fisheye.projectPoints(
                    point_3d[:, np.newaxis, :], np.zeros(3), np.zeros(3), ori_outK, dist[:4]
                )
                point_2d_original = point_2d_original[0, 0]
            else:
                # 针孔相机：使用畸变参数
                point_2d_original, _ = cv2.projectPoints(
                    point_3d, np.zeros(3), np.zeros(3), outK, dist
                )
                point_2d_original = point_2d_original[0, 0]

            logger.debug(f"point2d_original: {point_2d_original}")

        return {
            'type': 'Nearest',
            'point_2d_undist': point_2d_undist[0],  # 去畸变图像坐标
            'point_2d_original': point_2d_original,  # 原图坐标
            'point_2d': point_2d_original,  # 保持向后兼容，默认返回原图坐标
            'point_3d': box_res['interactioin_on_ray_seg'][1],
            'color': (0, 255, 0)  # 青色
        }

    return None

def draw_gaze_point_on_image(img, gaze_coord_info, save_path=None):
    """在图像上绘制gaze落点并保存"""
    if gaze_coord_info is None:
        logger.warning("没有有效的gaze坐标信息")
        return img

    img_with_gaze = img.copy()
    point_2d = gaze_coord_info['point_2d']
    color = gaze_coord_info['color']
    gaze_type = gaze_coord_info['type']

    # 绘制gaze落点
    cv2.circle(img_with_gaze, tuple(point_2d.astype(int)), 3, color, -1)

    # 添加文字标注
    # text = f"{gaze_type}: ({point_2d[0]}, {point_2d[1]})"
    # cv2.putText(img_with_gaze, text, (point_2d[0] + 15, point_2d[1] - 15),
    #             cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    # 保存图片（仅在DEBUG模式下）
    if save_path and DEBUG_SAVE_IMAGES:
        cv2.imwrite(save_path, img_with_gaze)
        logger.info(f"已保存带gaze落点的图片到: {save_path}")
    elif save_path and not DEBUG_SAVE_IMAGES:
        logger.debug(f"DEBUG模式关闭，跳过保存图片: {save_path}")

    return img_with_gaze

def init_model():
    global MODELSWRAPPER, MODELS_INITIALIZED
    if MODELS_INITIALIZED:
        return
    with MODELS_INIT_LOCK:
        if not MODELS_INITIALIZED:
            MODELSWRAPPER = ModelsWarpper(
                PARAM_SETTING_PATH,
                MODULE_SETTING_PATH,
                CAMERA_PATH,
                PROVIDERS
            )
            MODELS_INITIALIZED = True
            logger.info("模型初始化完成")

def recognize_gaze_sync(gaze_input, outcar_img_path, prompt="", call_jarvis=False, stream=False):
    """
    使用gaze信息和outcar图片进行车辆识别

    Args:
        gaze_input: dict包含mix_gaze, gaze_origin, gaze_valid
        outcar_img_path: outcar图片路径
        prompt: 识别提示词
        call_jarvis: bool, 是否调用JARVIS API进行车辆识别
        stream: bool, 是否使用流式调用JARVIS API
    """
    init_model()  # 每次调用前确认初始化
    start_time = time.time()
    img_out = cv2.imread(outcar_img_path)
    if img_out is None:
        return {"code": ERROR_CODES["IMAGE_LOAD_FAIL"], "output": None, "error_msg": "无法加载图片文件"}

    try:
        # 构建gaze输入，参考run_gazevec.py的格式
        gaze_data = {
            "mix_gaze": np.array(gaze_input["mix_gaze"]),
            "gaze_origin": np.array(gaze_input["gaze_origin"]),
            "gaze_valid": gaze_input["gaze_valid"]
        }

        # 使用run_gazevec方法处理
        request_id = next(_request_id_iter)
        MODELSWRAPPER.run_gazevec(gaze_data, img_out, request_id)
        models_result = MODELSWRAPPER.get_logui_info()
        # print("models_result:",models_result)
        output_result = MODELSWRAPPER.get_output_info()
        # print("output_result:",output_result)

        # 从相机配置文件读取真实的相机参数
        try:
            with open(CAMERA_PATH, 'r') as f:
                camera_config = json.load(f)

            # 读取OutcarCamera的相机内参和畸变参数
            outK = np.array(camera_config['OutcarCamera']['CameraIntrinsics'])
            outdist = np.array(camera_config['OutcarCamera']['Distortion'])

            logger.info("使用真实相机参数:")
            logger.info(f"内参矩阵: {outK.tolist()}")
            logger.info(f"畸变参数: {outdist.tolist()}")

        except Exception as e:
            logger.warning(f"读取相机参数失败: {e}")
            # 使用备用参数
            outK = np.array([[899.96, 0, 949.87],[0, 906.91, 538.26],[0, 0, 1]])
            outdist = np.array([-0.08173804, 0.01894376, -0.02756283, 0.01047146])

        # 获取gaze落点的2D坐标
        gaze_coord_info = get_gaze_2d_coordinates(models_result, outK, outdist, camera_config)

        if gaze_coord_info:
            logger.info("Gaze落点信息:")
            logger.info(f"类型: {gaze_coord_info['type']}")
            if 'point_2d_undist' in gaze_coord_info:
                logger.info(f"去畸变图像坐标: ({gaze_coord_info['point_2d_undist'][0]:.2f}, {gaze_coord_info['point_2d_undist'][1]:.2f})")
                logger.info(f"原图坐标: ({gaze_coord_info['point_2d_original'][0]:.2f}, {gaze_coord_info['point_2d_original'][1]:.2f})")
            else:
                logger.info(f"2D坐标: ({gaze_coord_info['point_2d'][0]:.2f}, {gaze_coord_info['point_2d'][1]:.2f})")
            logger.info(f"3D坐标: {gaze_coord_info['point_3d']}")

            # 生成保存路径（仅在DEBUG模式下）
            save_path = None
            if DEBUG_SAVE_IMAGES:
                timestamp = int(time.time() * 1000)
                save_path = f"recognition_app/temp_images/gaze_point_{timestamp}.png"

            # 在图像上绘制gaze落点并保存
            img_with_gaze = draw_gaze_point_on_image(img_out, gaze_coord_info, save_path)
        else:
            logger.error("未找到有效的gaze落点坐标")

        gaze_list = output_result.get("GazeSelected", [])

        if not gaze_list:
            logger.error("裁剪失败：模型未返回任何检测框")
            return {"code": ERROR_CODES["CROP_FAIL"], "output": None, "error_msg": "模型未返回任何检测框"}
        logger.debug(f"gaze_list: {gaze_list}")
        # ms
        logger.info("执行时间: {:.2f} ms".format((time.time() - start_time) * 1000))
        # 分析不同rect的坐标
        gaze_item = gaze_list[0]
        logger.info("不同Rect坐标分析:")
        logger.info(f"2DRect (缩放): {gaze_item['2DRect']}")
        logger.info(f"2DRect_ori (原始): {gaze_item['2DRect_ori']}")
        logger.info(f"Undist2DRect (去畸变): {gaze_item['Undist2DRect']}")

        # 使用2DRect_ori进行裁剪，因为gaze落点计算也是基于原始图像坐标系
        x1, y1, x2, y2 = gaze_item["2DRect_ori"]
        logger.info(f"使用2DRect_ori进行裁剪: [{x1}, {y1}, {x2}, {y2}]")
        h, w = img_out.shape[:2]
        crop_img = img_out[max(0,y1):min(h,y2), max(0,x1):min(w,x2)]

        # 保存裁剪图片（仅在DEBUG模式下）
        if DEBUG_SAVE_IMAGES:
            cv2.imwrite("recognition_app/temp_images/crop_img.png", crop_img)
            logger.info("已保存裁剪图片到: recognition_app/temp_images/crop_img.png")

        if crop_img.size == 0:
            raise ValueError("裁剪区域为空")
        rects = gaze_item['2DRects']
        logger.info(f"检测到以下Rect: {rects}")
        for rect in rects:
            cv2.rectangle(img_out, tuple(rect[:2]), tuple(rect[2:]), (255, 0, 0), 3)

        # 保存带检测框的图片（仅在DEBUG模式下）
        if DEBUG_SAVE_IMAGES:
            cv2.imwrite("recognition_app/temp_images/rect_img.png", img_out)
            logger.info("已保存带检测框图片到: recognition_app/temp_images/rect_img.png")

        logger.info("裁剪完成")
    except Exception as e:
        logger.error(f"裁剪失败: {e}")
        return {"code": ERROR_CODES["CROP_FAIL"], "output": None, "error_msg": f"裁剪失败: {str(e)}"}

    # 根据参数和prompt决定是否调用JARVIS API
    if call_jarvis and prompt and prompt.strip():
        try:
            start_time = time.time()
            _, img_bytes = cv2.imencode(".png", crop_img)
            files = {"image": ("image.png", img_bytes.tobytes(), "image/png")}
            payload = {
                "model": "byd-senseauto",
                "messages": [{"query_id": 1, "user_query": prompt}],
                "stream": stream  # 根据参数决定是否流式
            }

            request_time = time.time()

            if stream:
                # 流式调用
                logger.info("使用流式调用JARVIS API")
                response = requests.post(
                    JARVIS_API,
                    files=files,
                    data={"data": json.dumps(payload)},
                    headers=HEADERS,
                    stream=True  # 启用流式响应
                )

                if response.status_code != 200:
                    return {"code": ERROR_CODES["API_REQUEST_FAIL"], "output": None, "error_msg": f"API请求失败，状态码: {response.status_code}"}

                # 处理流式响应
                api_output, jarvis_id = _process_streaming_response(response, request_time)
                if api_output is None:
                    return {"code": ERROR_CODES["RESPONSE_PARSE_FAIL"], "output": None, "error_msg": "流式响应解析失败"}

            else:
                # 非流式调用
                logger.info("使用非流式调用JARVIS API")
                response = requests.post(
                    JARVIS_API,
                    files=files,
                    data={"data": json.dumps(payload)},
                    headers=HEADERS
                )

                logger.info(f"接口请求时间: {round(time.time() - request_time, 2)}秒")

                if response.status_code != 200:
                    return {"code": ERROR_CODES["API_REQUEST_FAIL"], "output": None, "error_msg": f"API请求失败，状态码: {response.status_code}"}

                # 处理非流式响应
                api_output, jarvis_id = _process_non_streaming_response(response)
                if api_output is None:
                    return {"code": ERROR_CODES["RESPONSE_PARSE_FAIL"], "output": None, "error_msg": "非流式响应解析失败"}

            logger.info(f"JARVIS API识别结果: {api_output}")
            logger.info(f"JARVIS ID: {jarvis_id}")
            # 成功调用JARVIS API
            error_msg = "OK"

        except Exception as e:
            logger.error(f"接口调用失败: {e}")
            return {"code": ERROR_CODES["RESPONSE_PARSE_FAIL"], "output": None, "error_msg": f"接口调用失败: {str(e)}"}
    else:
        # 不调用API时返回默认信息
        jarvis_id = None  # 未调用API时没有jarvis_id
        api_output = None  # output字段只用于JARVIS返回结果
        if not call_jarvis:
            error_msg = "未启用车辆识别"
            logger.info("call_jarvis=False，跳过JARVIS API调用")
        elif not prompt or not prompt.strip():
            error_msg = "prompt为空，跳过车辆识别"
            logger.info("prompt为空，跳过JARVIS API调用")
        else:
            error_msg = "未调用车辆识别API"
            logger.info("跳过JARVIS API调用")

    # 构建返回数据，包含gaze相关信息
    gaze_point = None
    if 'gaze_coord_info' in locals() and gaze_coord_info:
        # 只返回point_2d信息，转换为Python列表以便JSON序列化
        point_2d = gaze_coord_info.get("point_2d").astype(int)
        if hasattr(point_2d, 'tolist'):
            gaze_point = point_2d.tolist()
        elif isinstance(point_2d, (list, tuple)):
            gaze_point = [float(x) for x in point_2d]
        else:
            gaze_point = point_2d

    # 构建返回结果
    result = {
        "code": ERROR_CODES["SUCCESS"],
        "output": api_output,
        "itemID": gaze_item.get("itemID", ""),
        "2DRect_ori": gaze_item.get("2DRect_ori", []),
        "2DRects": gaze_item.get("2DRects", []),
        "gaze_point": gaze_point,
        "id": jarvis_id,
        "error_msg": error_msg
    }

    return result

def recognize_gaze_streaming(gaze_input, outcar_img_path, prompt="", call_jarvis=False):
    """
    流式版本的gaze识别函数 - 返回生成器用于流式输出

    Args:
        gaze_input: dict包含mix_gaze, gaze_origin, gaze_valid
        outcar_img_path: outcar图片路径
        prompt: 识别提示词
        call_jarvis: bool, 是否调用JARVIS API进行车辆识别
    """
    # 检查文件是否存在
    import os
    if not os.path.exists(outcar_img_path):
        logger.error(f"流式函数: 文件不存在 {outcar_img_path}")
        yield {
            "type": "error",
            "data": {"code": ERROR_CODES["IMAGE_READ_FAIL"], "output": None, "error_msg": f"文件不存在: {outcar_img_path}"}
        }
        return

    # 如果不调用JARVIS API，直接返回非流式结果
    if not call_jarvis or not prompt or not prompt.strip():
        result = recognize_gaze_sync(gaze_input, outcar_img_path, prompt, call_jarvis, False)
        yield {
            "type": "final",
            "data": result
        }
        return

    # 执行gaze检测部分（非流式）
    try:
        # 这里复制gaze检测的核心逻辑，但不调用JARVIS API
        start_time = time.time()
        logger.info("开始gaze识别...")

        # 解析gaze输入
        mix_gaze = gaze_input["mix_gaze"]
        gaze_origin = gaze_input["gaze_origin"]
        gaze_valid = gaze_input["gaze_valid"]

        # 读取图片
        img_out = cv2.imread(outcar_img_path)
        if img_out is None:
            logger.error(f"流式函数无法读取图片: {outcar_img_path}")
            yield {
                "type": "error",
                "data": {"code": ERROR_CODES["IMAGE_READ_FAIL"], "output": None, "error_msg": f"无法读取图片: {outcar_img_path}"}
            }
            return

        # 执行gaze检测
        gaze_list, gaze_coord_info = MODELSWRAPPER.gaze_detect(
            img_out, mix_gaze, gaze_origin, gaze_valid
        )

        if not gaze_list:
            yield {
                "type": "error",
                "data": {"code": ERROR_CODES["NO_GAZE_DETECTED"], "output": None, "error_msg": "未检测到有效的gaze信息"}
            }
            return

        gaze_item = gaze_list[0]
        x1, y1, x2, y2 = gaze_item["2DRect_ori"]
        h, w = img_out.shape[:2]
        crop_img = img_out[max(0,y1):min(h,y2), max(0,x1):min(w,x2)]

        if crop_img.size == 0:
            yield {
                "type": "error",
                "data": {"code": ERROR_CODES["CROP_FAIL"], "output": None, "error_msg": "裁剪区域为空"}
            }
            return

        # 准备gaze相关信息
        gaze_point = None
        if gaze_coord_info:
            point_2d = gaze_coord_info.get("point_2d").astype(int)
            if hasattr(point_2d, 'tolist'):
                gaze_point = point_2d.tolist()
            elif isinstance(point_2d, (list, tuple)):
                gaze_point = [float(x) for x in point_2d]
            else:
                gaze_point = point_2d

        # 基础返回数据
        base_data = {
            "code": ERROR_CODES["SUCCESS"],
            "itemID": gaze_item.get("itemID", ""),
            "2DRect_ori": gaze_item.get("2DRect_ori", []),
            "2DRects": gaze_item.get("2DRects", []),
            "gaze_point": gaze_point
        }

        # 流式调用JARVIS API
        _, img_bytes = cv2.imencode(".png", crop_img)
        files = {"image": ("image.png", img_bytes.tobytes(), "image/png")}
        payload = {
            "model": "byd-senseauto",
            "messages": [{"query_id": 1, "user_query": prompt}],
            "stream": True
        }

        logger.info("开始流式调用JARVIS API")
        request_time = time.time()

        response = requests.post(
            JARVIS_API,
            files=files,
            data={"data": json.dumps(payload)},
            headers=HEADERS,
            stream=True
        )

        if response.status_code != 200:
            yield {
                "type": "error",
                "data": {"code": ERROR_CODES["API_REQUEST_FAIL"], "output": None, "error_msg": f"API请求失败，状态码: {response.status_code}"}
            }
            return

        # 流式处理JARVIS响应
        for chunk_data in _process_streaming_response_generator(response, request_time):
            if chunk_data["type"] == "chunk":
                # 返回流式数据块
                yield {
                    "type": "chunk",
                    "data": {
                        **base_data,
                        "output": chunk_data["content"],
                        "jarvis_id": chunk_data["jarvis_id"],
                        "stream_state": chunk_data["state"]
                    }
                }
            elif chunk_data["type"] == "final":
                # 返回最终结果
                yield {
                    "type": "final",
                    "data": {
                        **base_data,
                        "output": chunk_data["content"],
                        "jarvis_id": chunk_data["jarvis_id"]
                    }
                }
                return
            elif chunk_data["type"] == "error":
                yield {
                    "type": "error",
                    "data": {"code": ERROR_CODES["RESPONSE_PARSE_FAIL"], "output": None, "error_msg": chunk_data["content"]}
                }
                return

    except Exception as e:
        logger.error(f"流式识别失败: {e}")
        yield {
            "type": "error",
            "data": {"code": ERROR_CODES["RESPONSE_PARSE_FAIL"], "output": None, "error_msg": f"流式识别失败: {str(e)}"}
        }

async def recognize_gaze_async(gaze_input, outcar_img_path, prompt="", call_jarvis=False, stream=False):
    """
    异步版本的gaze识别函数

    Args:
        gaze_input: dict包含mix_gaze, gaze_origin, gaze_valid
        outcar_img_path: outcar图片路径
        prompt: 识别提示词
        call_jarvis: bool, 是否调用JARVIS API进行车辆识别
        stream: bool, 是否使用流式调用JARVIS API
    """
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(
        executor,
        recognize_gaze_sync,
        gaze_input,
        outcar_img_path,
        prompt,
        call_jarvis,
        stream
    )

def recognize_gaze_memory_streaming(gaze_input, img_array, prompt="", call_jarvis=False):
    """
    内存版本的流式gaze识别函数 - 直接处理图片数组，不保存临时文件

    Args:
        gaze_input: dict包含mix_gaze, gaze_origin, gaze_valid
        img_array: numpy数组格式的图片数据
        prompt: 识别提示词
        call_jarvis: bool, 是否调用JARVIS API进行车辆识别
    """
    try:
        init_model()  # 确保模型已初始化

        # 如果不调用JARVIS API，直接返回非流式结果
        if not call_jarvis or not prompt or not prompt.strip():
            result = recognize_gaze_memory_sync(gaze_input, img_array, prompt, call_jarvis)
            yield {
                "type": "final",
                "data": result
            }
            return

        logger.info("开始内存流式gaze识别...")
        start_time = time.time()

        # 解析gaze输入
        mix_gaze = gaze_input["mix_gaze"]
        gaze_origin = gaze_input["gaze_origin"]
        gaze_valid = gaze_input["gaze_valid"]

        # 构建gaze输入，参考run_gazevec.py的格式
        gaze_data = {
            "mix_gaze": np.array(mix_gaze),
            "gaze_origin": np.array(gaze_origin),
            "gaze_valid": gaze_valid
        }

        # 使用run_gazevec方法处理
        request_id = next(_request_id_iter)
        MODELSWRAPPER.run_gazevec(gaze_data, img_array, request_id)
        models_result = MODELSWRAPPER.get_logui_info()
        output_result = MODELSWRAPPER.get_output_info()

        # 使用与原来相同的方式获取检测框信息
        gaze_list = output_result.get("GazeSelected", [])

        if not gaze_list:
            logger.warning("未从output_result中获取到GazeSelected，尝试从models_result构建")
            # 如果没有GazeSelected，尝试从models_result构建
            gaze_list = []
            gaze_coord_info = {}

        if not gaze_list and models_result.get('boxRes', {}).get('valid', False):
            # 构建gaze项目信息
            box_res = models_result['boxRes']

            # 调试：打印box_res的内容
            logger.info(f"boxRes内容: {box_res}")

            # 从box_res中提取2D矩形信息
            rect_2d = box_res.get('2DRect', box_res.get('2DRect_ori', []))
            if not rect_2d and 'box_corner' in box_res:
                # 如果有3D角点，投影到2D
                try:
                    # 从相机配置文件读取相机参数
                    with open(CAMERA_PATH, 'r') as f:
                        camera_config = json.load(f)
                    outK = np.array(camera_config['OutcarCamera']['CameraIntrinsics'])
                    outdist = np.array(camera_config['OutcarCamera']['Distortion'])

                    # 投影3D角点到2D
                    corners_3d = np.array(box_res['box_corner'])
                    corners_2d = pt3dto2d(corners_3d, outK, outdist)

                    if corners_2d.size > 0:
                        x_coords = corners_2d[:, 0]
                        y_coords = corners_2d[:, 1]
                        rect_2d = [int(np.min(x_coords)), int(np.min(y_coords)),
                                  int(np.max(x_coords)), int(np.max(y_coords))]
                        logger.info(f"投影得到2D矩形: {rect_2d}")
                except Exception as e:
                    logger.error(f"3D到2D投影失败: {e}")
                    rect_2d = []

            gaze_item = {
                "itemID": f"vehicle_{box_res.get('box_idx', 0)}",
                "2DRect_ori": rect_2d,
                "2DRects": [rect_2d] if rect_2d else []
            }
            gaze_list.append(gaze_item)
        else:
            logger.info(f"从output_result获取到 {len(gaze_list)} 个检测框")
            if gaze_list:
                gaze_item = gaze_list[0]
                logger.info("检测框坐标分析:")
                logger.info(f"2DRect (缩放): {gaze_item.get('2DRect', 'N/A')}")
                logger.info(f"2DRect_ori (原始): {gaze_item.get('2DRect_ori', 'N/A')}")
                logger.info(f"Undist2DRect (去畸变): {gaze_item.get('Undist2DRect', 'N/A')}")

        # 使用与原来相同的方法计算gaze落点
        try:
            # 从相机配置文件读取相机参数
            with open(CAMERA_PATH, 'r') as f:
                camera_config = json.load(f)
            outK = np.array(camera_config['OutcarCamera']['CameraIntrinsics'])
            outdist = np.array(camera_config['OutcarCamera']['Distortion'])

            # 使用get_gaze_2d_coordinates函数计算精确的gaze落点
            gaze_coord_info = get_gaze_2d_coordinates(models_result, outK, outdist, camera_config)

            if not gaze_coord_info:
                # 如果计算失败，使用默认值
                gaze_coord_info = {
                    'point_2d': [0, 0],
                    'color': (0, 255, 0),
                    'type': 'gaze'
                }
                logger.warning("gaze落点计算失败，使用默认值")
            else:
                logger.info(f"计算得到gaze落点: {gaze_coord_info.get('point_2d', [0, 0])}")

        except Exception as e:
            logger.error(f"gaze落点计算失败: {e}")
            # 使用默认值
            gaze_coord_info = {
                'point_2d': [0, 0],
                'color': (0, 255, 0),
                'type': 'gaze'
            }

        if not gaze_list:
            yield {
                "type": "error",
                "data": {"code": ERROR_CODES["NO_GAZE_DETECTED"], "output": None, "error_msg": "未检测到有效的gaze信息"}
            }
            return

        # 获取第一个有效的gaze项目
        gaze_item = gaze_list[0]
        logger.info(f"检测到gaze项目: {gaze_item.get('itemID', 'unknown')}")

        # 获取裁剪区域
        rect_2d = gaze_item.get("2DRect_ori", [])
        if not rect_2d or len(rect_2d) != 4:
            yield {
                "type": "error",
                "data": {"code": ERROR_CODES["CROP_FAIL"], "output": None, "error_msg": "无效的2D矩形区域"}
            }
            return

        # 裁剪图片
        x1, y1, x2, y2 = map(int, rect_2d)
        crop_img = img_array[y1:y2, x1:x2]

        if crop_img.size == 0:
            yield {
                "type": "error",
                "data": {"code": ERROR_CODES["CROP_FAIL"], "output": None, "error_msg": "裁剪区域为空"}
            }
            return

        # 获取gaze落点
        gaze_point = gaze_coord_info.get('point_2d', [0, 0])
        if isinstance(gaze_point, np.ndarray):
            gaze_point = gaze_point.tolist()

        # 基础返回数据
        base_data = {
            "code": ERROR_CODES["SUCCESS"],
            "itemID": gaze_item.get("itemID", ""),
            "2DRect_ori": gaze_item.get("2DRect_ori", []),
            "2DRects": gaze_item.get("2DRects", []),
            "gaze_point": gaze_point
        }

        # 流式调用JARVIS API
        _, img_bytes = cv2.imencode(".png", crop_img)
        files = {"image": ("image.png", img_bytes.tobytes(), "image/png")}
        payload = {
            "model": "byd-senseauto",
            "messages": [{"query_id": 1, "user_query": prompt}],
            "stream": True
        }

        logger.info("开始流式调用JARVIS API")
        request_time = time.time()

        response = requests.post(
            JARVIS_API,
            files=files,
            data={"data": json.dumps(payload)},
            headers=HEADERS,
            stream=True
        )

        if response.status_code != 200:
            yield {
                "type": "error",
                "data": {"code": ERROR_CODES["API_REQUEST_FAIL"], "output": None, "error_msg": f"API请求失败，状态码: {response.status_code}"}
            }
            return

        # 流式处理JARVIS响应
        for chunk_data in _process_streaming_response_generator(response, request_time):
            if chunk_data["type"] == "chunk":
                # 返回流式数据块
                yield {
                    "type": "chunk",
                    "data": {
                        **base_data,
                        "output": chunk_data["content"],
                        "jarvis_id": chunk_data["jarvis_id"],
                        "stream_state": chunk_data["state"]
                    }
                }
            elif chunk_data["type"] == "final":
                # 返回最终结果
                yield {
                    "type": "final",
                    "data": {
                        **base_data,
                        "output": chunk_data["content"],
                        "jarvis_id": chunk_data["jarvis_id"]
                    }
                }
                return
            elif chunk_data["type"] == "error":
                yield {
                    "type": "error",
                    "data": {"code": ERROR_CODES["RESPONSE_PARSE_FAIL"], "output": None, "error_msg": chunk_data["content"]}
                }
                return

    except Exception as e:
        logger.error(f"内存流式识别失败: {e}")
        yield {
            "type": "error",
            "data": {"code": ERROR_CODES["RESPONSE_PARSE_FAIL"], "output": None, "error_msg": f"内存流式识别失败: {str(e)}"}
        }

def recognize_gaze_memory_sync(gaze_input, img_array, prompt="", call_jarvis=False):
    """
    内存版本的同步gaze识别函数 - 直接处理图片数组，不保存临时文件

    Args:
        gaze_input: dict包含mix_gaze, gaze_origin, gaze_valid
        img_array: numpy数组格式的图片数据
        prompt: 识别提示词
        call_jarvis: bool, 是否调用JARVIS API进行车辆识别
    """
    init_model()  # 每次调用前确认初始化
    start_time = time.time()

    if img_array is None:
        return {"code": ERROR_CODES["IMAGE_LOAD_FAIL"], "output": None, "error_msg": "无法加载图片数据"}

    try:
        # 构建gaze输入，参考run_gazevec.py的格式
        gaze_data = {
            "mix_gaze": np.array(gaze_input["mix_gaze"]),
            "gaze_origin": np.array(gaze_input["gaze_origin"]),
            "gaze_valid": gaze_input["gaze_valid"]
        }

        logger.info(f"开始gaze检测 - mix_gaze: {gaze_data['mix_gaze']}, gaze_origin: {gaze_data['gaze_origin']}, gaze_valid: {gaze_data['gaze_valid']}")

        # 使用run_gazevec方法处理
        request_id = next(_request_id_iter)
        MODELSWRAPPER.run_gazevec(gaze_data, img_array, request_id)
        models_result = MODELSWRAPPER.get_logui_info()
        output_result = MODELSWRAPPER.get_output_info()

        # 使用与原来相同的方式获取检测框信息
        gaze_list = output_result.get("GazeSelected", [])

        if not gaze_list:
            logger.warning("未从output_result中获取到GazeSelected，尝试从models_result构建")
            # 如果没有GazeSelected，尝试从models_result构建
            gaze_list = []
            gaze_coord_info = {}

        if not gaze_list and models_result.get('boxRes', {}).get('valid', False):
            # 构建gaze项目信息
            box_res = models_result['boxRes']

            # 调试：打印box_res的内容
            logger.info(f"boxRes内容: {box_res}")

            # 从box_res中提取2D矩形信息
            rect_2d = box_res.get('2DRect', box_res.get('2DRect_ori', []))
            if not rect_2d and 'box_corner' in box_res:
                # 如果有3D角点，投影到2D
                try:
                    # 从相机配置文件读取相机参数
                    with open(CAMERA_PATH, 'r') as f:
                        camera_config = json.load(f)
                    outK = np.array(camera_config['OutcarCamera']['CameraIntrinsics'])
                    outdist = np.array(camera_config['OutcarCamera']['Distortion'])

                    # 投影3D角点到2D
                    corners_3d = np.array(box_res['box_corner'])
                    corners_2d = pt3dto2d(corners_3d, outK, outdist)

                    if corners_2d.size > 0:
                        x_coords = corners_2d[:, 0]
                        y_coords = corners_2d[:, 1]
                        rect_2d = [int(np.min(x_coords)), int(np.min(y_coords)),
                                  int(np.max(x_coords)), int(np.max(y_coords))]
                        logger.info(f"投影得到2D矩形: {rect_2d}")
                except Exception as e:
                    logger.error(f"3D到2D投影失败: {e}")
                    rect_2d = []

            gaze_item = {
                "itemID": f"vehicle_{box_res.get('box_idx', 0)}",
                "2DRect_ori": rect_2d,
                "2DRects": [rect_2d] if rect_2d else []
            }
            gaze_list.append(gaze_item)
        else:
            logger.info(f"从output_result获取到 {len(gaze_list)} 个检测框")
            if gaze_list:
                gaze_item = gaze_list[0]
                logger.info("检测框坐标分析:")
                logger.info(f"2DRect (缩放): {gaze_item.get('2DRect', 'N/A')}")
                logger.info(f"2DRect_ori (原始): {gaze_item.get('2DRect_ori', 'N/A')}")
                logger.info(f"Undist2DRect (去畸变): {gaze_item.get('Undist2DRect', 'N/A')}")

        # 使用与原来相同的方法计算gaze落点
        try:
            # 从相机配置文件读取相机参数
            with open(CAMERA_PATH, 'r') as f:
                camera_config = json.load(f)
            outK = np.array(camera_config['OutcarCamera']['CameraIntrinsics'])
            outdist = np.array(camera_config['OutcarCamera']['Distortion'])

            # 使用get_gaze_2d_coordinates函数计算精确的gaze落点
            gaze_coord_info = get_gaze_2d_coordinates(models_result, outK, outdist, camera_config)

            if not gaze_coord_info:
                # 如果计算失败，使用默认值
                gaze_coord_info = {
                    'point_2d': [0, 0],
                    'color': (0, 255, 0),
                    'type': 'gaze'
                }
                logger.warning("gaze落点计算失败，使用默认值")
            else:
                logger.info(f"计算得到gaze落点: {gaze_coord_info.get('point_2d', [0, 0])}")

        except Exception as e:
            logger.error(f"gaze落点计算失败: {e}")
            # 使用默认值
            gaze_coord_info = {
                'point_2d': [0, 0],
                'color': (0, 255, 0),
                'type': 'gaze'
            }

        logger.info(f"gaze检测完成 - 检测到 {len(gaze_list)} 个项目")

        if not gaze_list:
            logger.warning("未检测到有效的gaze信息")
            return {"code": ERROR_CODES["NO_GAZE_DETECTED"], "output": None, "error_msg": "未检测到有效的gaze信息"}

        # 获取第一个有效的gaze项目
        gaze_item = gaze_list[0]
        logger.info(f"使用gaze项目: {gaze_item}")

        # 获取gaze落点
        gaze_point = gaze_coord_info.get('point_2d', [0, 0])
        if isinstance(gaze_point, np.ndarray):
            gaze_point = gaze_point.tolist()

        logger.info(f"gaze落点: {gaze_point}")

        # 获取裁剪区域
        rect_2d = gaze_item.get("2DRect_ori", [])
        if not rect_2d or len(rect_2d) != 4:
            logger.error(f"无效的2D矩形区域: {rect_2d}")
            return {"code": ERROR_CODES["CROP_FAIL"], "output": None, "error_msg": "无效的2D矩形区域"}

        logger.info(f"裁剪区域: {rect_2d}")

        # 裁剪图片
        x1, y1, x2, y2 = map(int, rect_2d)

        # 确保裁剪区域在图片范围内
        img_h, img_w = img_array.shape[:2]
        x1 = max(0, min(x1, img_w))
        y1 = max(0, min(y1, img_h))
        x2 = max(x1, min(x2, img_w))
        y2 = max(y1, min(y2, img_h))

        crop_img = img_array[y1:y2, x1:x2]

        if crop_img.size == 0:
            logger.error("裁剪区域为空")
            return {"code": ERROR_CODES["CROP_FAIL"], "output": None, "error_msg": "裁剪区域为空"}

        logger.info("裁剪完成")

    except Exception as e:
        logger.error(f"裁剪失败: {e}")
        return {"code": ERROR_CODES["CROP_FAIL"], "output": None, "error_msg": f"裁剪失败: {str(e)}"}

    # 根据参数和prompt决定是否调用JARVIS API
    if call_jarvis and prompt and prompt.strip():
        try:
            start_time = time.time()
            _, img_bytes = cv2.imencode(".png", crop_img)
            files = {"image": ("image.png", img_bytes.tobytes(), "image/png")}
            payload = {
                "model": "byd-senseauto",
                "messages": [{"query_id": 1, "user_query": prompt}],
                "stream": False  # 同步版本不使用流式
            }

            request_time = time.time()

            # 非流式调用
            logger.info("使用非流式调用JARVIS API")
            response = requests.post(
                JARVIS_API,
                files=files,
                data={"data": json.dumps(payload)},
                headers=HEADERS
            )

            logger.info(f"接口请求时间: {round(time.time() - request_time, 2)}秒")

            if response.status_code != 200:
                return {"code": ERROR_CODES["API_REQUEST_FAIL"], "output": None, "error_msg": f"API请求失败，状态码: {response.status_code}"}

            # 处理非流式响应
            api_output, jarvis_id = _process_non_streaming_response(response)
            if api_output is None:
                return {"code": ERROR_CODES["RESPONSE_PARSE_FAIL"], "output": None, "error_msg": "非流式响应解析失败"}

            logger.info(f"JARVIS API识别结果: {api_output}")
            logger.info(f"JARVIS ID: {jarvis_id}")
            # 成功调用JARVIS API
            error_msg = "OK"

        except Exception as e:
            logger.error(f"JARVIS API调用失败: {e}")
            return {"code": ERROR_CODES["API_REQUEST_FAIL"], "output": None, "error_msg": f"JARVIS API调用失败: {str(e)}"}
    else:
        # 不调用JARVIS API
        api_output = None
        jarvis_id = None
        error_msg = "OK"

    # 构建返回结果
    result = {
        "code": ERROR_CODES["SUCCESS"],
        "itemID": gaze_item.get("itemID", ""),
        "2DRect_ori": gaze_item.get("2DRect_ori", []),
        "2DRects": gaze_item.get("2DRects", []),
        "gaze_point": gaze_point,
        "output": api_output,
        "jarvis_id": jarvis_id,
        "error_msg": error_msg
    }

    total_time = time.time() - start_time
    logger.info(f"内存识别完成 - 总耗时: {total_time:.2f}秒")
    return result
