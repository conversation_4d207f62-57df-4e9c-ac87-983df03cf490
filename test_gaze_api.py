#!/usr/bin/env python3
"""
测试新的gaze接口 - 支持call_jarvis参数控制 (HTTPS版本)
"""
import requests
import json
import urllib3

# 禁用SSL警告（仅用于测试自签名证书）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_gaze_api_scenario(scenario_name, test_data, expected_behavior):
    """测试特定场景"""

    # API端点 (HTTPS)
    url = "https://127.0.0.1:8066/recognize"
    api_key = "sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"
    image_path = "dvr_520.png"

    print(f"\n🧪 {scenario_name}")
    print("-" * 50)
    print(f"参数: {test_data}")
    print(f"预期: {expected_behavior}")

    try:
        with open(image_path, "rb") as f:
            files = {"outcar_image": ("dvr_520.png", f, "image/png")}
            headers = {"apl-key": api_key}

            print("发送请求...")
            # 跳过SSL验证（用于测试自签名证书）
            response = requests.post(url, files=files, data=test_data, headers=headers, verify=False)

            print(f"状态码: {response.status_code}")

            # 检查是否为流式响应
            if test_data.get("stream") == "true":
                print("流式响应内容:")
                print(response.text)
                return response.text
            elif response.status_code == 200:
                result = response.json()
                print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

                # 验证结果
                output = result.get("output", "")
                id_value = result.get("id")

                if "未启用车辆识别" in output:
                    print("✅ 结果: 未调用JARVIS API")
                    print(f"   ID字段: {id_value} (应该为null)")
                elif "prompt为空" in output:
                    print("✅ 结果: prompt为空，跳过API调用")
                    print(f"   ID字段: {id_value} (应该为null)")
                elif output and output not in ["未启用车辆识别", "prompt为空，跳过车辆识别"]:
                    print(f"✅ 结果: 成功调用JARVIS API，识别结果: {output}")
                    print(f"   ID字段: {id_value} (JARVIS API返回的ID)")
                else:
                    print(f"⚠️ 结果: 未知状态 - {output}")
                    print(f"   ID字段: {id_value}")

                return result
            else:
                print(f"❌ 请求失败: {response.text}")
                return None

    except FileNotFoundError:
        print(f"❌ 图片文件 {image_path} 不存在")
        return None
    except Exception as e:
        print(f"❌ 请求出错: {e}")
        return None

def test_all_scenarios():
    """测试所有场景"""

    print("🤖 测试gaze接口 - call_jarvis参数控制功能 (HTTPS)")
    print("=" * 60)

    # 基础gaze数据
    base_gaze_data = {
        "mix_gaze": json.dumps([0.37740352, -0.24775083, -0.89148018]),
        "gaze_origin": json.dumps([-29.56759495, -102.30740821, 520.42972422]),
        "gaze_valid": 3
    }

    # 场景1: 默认参数 (call_jarvis=false, prompt="")
    scenario1_data = base_gaze_data.copy()
    test_gaze_api_scenario(
        "场景1: 默认参数",
        scenario1_data,
        "不调用JARVIS API，返回'未启用车辆识别'"
    )

    # 场景2: call_jarvis=false, prompt不为空
    scenario2_data = base_gaze_data.copy()
    scenario2_data.update({
        "call_jarvis": False,
        "prompt": "这是什么车"
    })
    test_gaze_api_scenario(
        "场景2: call_jarvis=false, prompt不为空",
        scenario2_data,
        "不调用JARVIS API，返回'未启用车辆识别'"
    )

    # 场景3: call_jarvis=true, prompt为空
    scenario3_data = base_gaze_data.copy()
    scenario3_data.update({
        "call_jarvis": True,
        "prompt": ""
    })
    test_gaze_api_scenario(
        "场景3: call_jarvis=true, prompt为空",
        scenario3_data,
        "不调用JARVIS API，返回'prompt为空，跳过车辆识别'"
    )

    # 场景4: call_jarvis=true, prompt不为空 (应该调用API)
    scenario4_data = base_gaze_data.copy()
    scenario4_data.update({
        "call_jarvis": True,
        "prompt": "这是什么车"
    })
    test_gaze_api_scenario(
        "场景4: call_jarvis=true, prompt不为空",
        scenario4_data,
        "调用JARVIS API，返回实际识别结果"
    )

    # 场景5: 流式响应 (call_jarvis=true, prompt不为空, stream=true)
    scenario5_data = base_gaze_data.copy()
    scenario5_data.update({
        "call_jarvis": True,
        "prompt": "这是什么车",
        "stream": "true"
    })
    test_gaze_api_scenario(
        "场景5: 流式响应 (call_jarvis=true, prompt不为空, stream=true)",
        scenario5_data,
        "调用JARVIS API，以流式方式返回结果"
    )

    # 场景6: 非流式响应 (call_jarvis=true, prompt不为空, stream=false)
    scenario6_data = base_gaze_data.copy()
    scenario6_data.update({
        "call_jarvis": True,
        "prompt": "这是什么车",
        "stream": "false"
    })
    test_gaze_api_scenario(
        "场景6: 非流式响应 (call_jarvis=true, prompt不为空, stream=false)",
        scenario6_data,
        "调用JARVIS API，以非流式方式返回结果"
    )

    print(f"\n📝 测试总结:")
    print(f"   - 场景1: 默认行为，不调用API (id=null)")
    print(f"   - 场景2: 显式禁用API调用 (id=null)")
    print(f"   - 场景3: 启用API但prompt为空 (id=null)")
    print(f"   - 场景4: 完整调用API (id=JARVIS返回的ID)")
    print(f"   - 场景5: 流式响应模式")
    print(f"   - 场景6: 非流式响应模式")
    print(f"   - 调用条件: call_jarvis=True AND prompt不为空")
    print(f"   - ID字段: 来自JARVIS API响应，未调用时为null")

def test_gaze_api():
    """兼容性函数 - 测试默认场景 (HTTPS版本)"""

    # API端点
    url = "https://127.0.0.1:8443/recognize"
    api_key = "sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"

    # 测试数据（默认不调用API）
    test_data = {
        "mix_gaze": json.dumps([0.37740352, -0.24775083, -0.89148018]),
        "gaze_origin": json.dumps([-29.56759495, -102.30740821, 520.42972422]),
        "gaze_valid": 3
        # 注意: 不包含call_jarvis和prompt，使用默认值
    }

    # 图片文件
    image_path = "dvr_520.png"

    try:
        with open(image_path, "rb") as f:
            files = {"outcar_image": ("dvr_520.png", f, "image/png")}

            print("发送请求到gaze接口 (默认参数)...")
            headers = {"apl-key": api_key}
            # 跳过SSL验证（用于测试自签名证书）
            response = requests.post(url, files=files, data=test_data, headers=headers, verify=False)

            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")

            if response.status_code == 200:
                result = response.json()
                print(f"识别结果: {result}")

                # 显示ID字段信息
                id_value = result.get("id")
                if id_value:
                    print(f"JARVIS ID: {id_value}")
                else:
                    print(f"JARVIS ID: null (未调用API)")

                return result
            else:
                print(f"请求失败: {response.text}")
                return None

    except FileNotFoundError:
        print(f"图片文件 {image_path} 不存在")
        return None
    except Exception as e:
        print(f"请求出错: {e}")
        return None

if __name__ == "__main__":
    # 运行所有测试场景
    test_all_scenarios()